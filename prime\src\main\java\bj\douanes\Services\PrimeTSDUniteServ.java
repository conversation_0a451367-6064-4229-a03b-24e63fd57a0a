package bj.douanes.Services;

import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.DTO.PrimeTSDUniteDto;
import bj.douanes.Repository.PrimeTsdUniteRepository;
import lombok.RequiredArgsConstructor;

public interface PrimeTSDUniteServ {
    List<PrimeTSDUniteDto> findAllByCodeUniteAndIdRepartition( Long idRepartition);
}

@Service
@RequiredArgsConstructor
class PrimeTSDUniteServImpl implements PrimeTSDUniteServ {

    private final PrimeTsdUniteRepository primeTSDUniteRepository;

    @Override
    public List<PrimeTSDUniteDto> findAllByCodeUniteAndIdRepartition(Long idRepartition) {
        return primeTSDUniteRepository.findByIdRepartition(idRepartition);
    }
}