package bj.douanes.Repository;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import bj.douanes.Model.PrimeTsdAgent;

@Repository
public interface PrimeTsdAgentRepository extends JpaRepository<PrimeTsdAgent, Long>{
    

    // @Query("SELECT p FROM PrimeTsdAgent p WHERE p.rAgentRepartition.repartition.idRepartition = :idRepartition")
    // List<PrimeTsdAgent> findAllByIdRepartition(@Param("idRepartition") Long idRepartition);

    List<PrimeTsdAgent> findByrAgentRepartition_Repartition_IdRepartition(Long idRepartition);
}
