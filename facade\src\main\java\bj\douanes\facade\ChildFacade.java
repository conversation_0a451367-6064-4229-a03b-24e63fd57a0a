package bj.douanes.facade;

import java.util.List;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import bj.douanes.personal.model.AgentChildren;
import bj.douanes.personal.service.AgentChildServ;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface ChildFacade {
    
    AgentChildren getChildById(Long id);
    AgentChildren createChild(Long agentId,Object child);
    String deleteChild(Long id);
    List<AgentChildren> getChildAllList();
    AgentChildren update(Long id,Object child);
}

@Slf4j
@Service
@RequiredArgsConstructor
class ChildFacadeImlp implements ChildFacade{

    private final ModelMapper modelMapper;
    private final AgentChildServ agentChildServ;

    @Override
    public AgentChildren getChildById(Long id) {
        return agentChildServ.getChildById(id);
    }

    @Override
    public AgentChildren createChild(Long agentId,Object child) {
        var newChild = modelMapper.map(child, AgentChildren.class);
        return agentChildServ.createChild(agentId, newChild);
    }

    @Override
    public String deleteChild(Long id) {
        return agentChildServ.deleteChild(id);
    }

    @Override
    public List<AgentChildren> getChildAllList() {
        return agentChildServ.getChildAllList();
    }

    @Override
    public AgentChildren update(Long id, Object child) {
        log.info("Implementation de la mise a jour");
        AgentChildren agentChildren = agentChildServ.getChildById(id);
        log.info("Avant le mapping: {}", agentChildren);
        modelMapper.map(child, agentChildren);
        return agentChildServ.createChild(id, agentChildren);
    }
}