package bj.douanes.facade.Prime;

import java.util.List;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import bj.douanes.Model.TypePrime;
import bj.douanes.Services.PrimeServ;
import lombok.RequiredArgsConstructor;

public interface TypePrimeFacade {

    List<TypePrime> getAllPrimes();
    TypePrime getPrimeById(Long id);
    TypePrime createPrime(Object prime);
    TypePrime updatePrime(Long id, Object prime);
    void deletePrime(Long id);

}

@Service
@RequiredArgsConstructor 
class TypePrimeFacadeImpl implements TypePrimeFacade {

    private final PrimeServ primeServ;
    private final ModelMapper modelMapper;

    @Override
    public List<TypePrime> getAllPrimes() {
        return primeServ.getAllPrimes();
    }
    @Override
    public TypePrime getPrimeById(Long id) {
        return primeServ.getPrimeById(id);
    }
    @Override
    public TypePrime createPrime(Object prime) {
        var newPrime = modelMapper.map(prime, TypePrime.class);
        return primeServ.createPrime(newPrime);
    }
    @Override
    public TypePrime updatePrime(Long id, Object prime) {
        var primeToUpdate = modelMapper.map(prime, TypePrime.class);
        return primeServ.updatePrime(id, primeToUpdate);
    }
    @Override
    public void deletePrime(Long id) {
        primeServ.deletePrime(id);
    }
}

