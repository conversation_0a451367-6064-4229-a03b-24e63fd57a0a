package bj.douanes.sydoniaplusplus.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import org.springframework.data.annotation.Transient;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "OPS$ASY", name = "SAD_GEN")
public class SadGen {

    
    @Column("KEY_YEAR")
    private String keyYear;
    
    @Column("KEY_CUO")
    private String keyCuo;
    
    @Column("KEY_DEC")
    private String keyDec;
    
    @Column("KEY_NBER")
    private String keyNber;
    
    @Column("SAD_NUM")
    private BigDecimal sadNum;   

    @Column("SAD_FLW")
    private String sadFlw;

    @Column("SAD_TYP_DEC")
    private String sadTypDec;

    @Column("SAD_TYP_PROC")
    private String sadTypProc;

    @Column("SAD_TYP_TRANSIT")
    private String sadTypTransit;

    @Column("SAD_EXPORTER")
    private String sadExporter;

    @Column("SAD_MANIF_NBER")
    private String sadManifNber;

    @Column("SAD_LST_NBER")
    private BigDecimal sadLstNber;

    @Column("SAD_REG_SERIAL")
    private String sadRegSerial;

    @Column("SAD_REG_NBER")
    private BigDecimal sadRegNber;

    @Column("SAD_REG_DATE")
    private LocalDate sadRegDate;

    @Column("SAD_ITM_TOTAL")
    private BigDecimal sadItmTotal;

    @Column("SAD_PACK_TOTAL")
    private BigDecimal sadPackTotal;

    @Column("SAD_CONSIGNEE")
    private String sadConsignee;

    @Column("SAD_FINANCIAL")
    private String sadFinancial;

    @Column("SAD_CTY_1DLP")
    private String sadCty1dlp;

    @Column("SAD_TRA_CTY")
    private String sadTraCty;

    @Column("SAD_VAL_DETAILS")
    private BigDecimal sadValDetails;

    @Column("SAD_CAP_REF")
    private String sadCapRef;

    @Column("SAD_CTY_EXPCOD")
    private String sadCtyExpcod;

    @Column("SAD_CTY_EXPREG")
    private String sadCtyExpreg;

    @Column("SAD_CTY_DESTCOD")
    private String sadCtyDestcod;

    @Column("SAD_CTY_DESTREG")
    private String sadCtyDestreg;

    @Column("SAD_TRSP_IDDEPAR")
    private String sadTrspIddepar;

    @Column("SAD_TRSP_NATDEPAR")
    private String sadTrspNatdepar;

    @Column("SAD_CTNR_FLAG")
    private String sadCtnrFlag;

    @Column("SAD_TOD_COD")
    private String sadTodCod;

    @Column("SAD_TOD_NAM")
    private String sadTodNam;

    @Column("SAD_TOD_SIT")
    private String sadTodSit;

    @Column("SAD_TRSP_IDBORD")
    private String sadTrspIdbord;

    @Column("SAD_TRSP_NATBORD")
    private String sadTrspNatbord;

    @Column("SAD_CUR_COD")
    private String sadCurCod;

    @Column("SAD_TOT_INVOICED")
    private BigDecimal sadTotInvoiced;

    @Column("SAD_TRA_COD1")
    private String sadTraCod1;

    @Column("SAD_TRA_COD2")
    private String sadTraCod2;

    @Column("SAD_MOT_BORD")
    private String sadMotBord;

    @Column("SAD_MOT_INLAND")
    private String sadMotInland;

    @Column("SAD_LOP_COD")
    private String sadLopCod;

    @Column("SAD_TOP_COD")
    private String sadTopCod;

    @Column("SAD_CUO_BORD")
    private String sadCuoBord;

    @Column("SAD_LOC_GOODS")
    private String sadLocGoods;

    @Column("SAD_BNK_COD")
    private String sadBnkCod;

    @Column("SAD_BNK_BRA")
    private String sadBnkBra;

    @Column("SAD_BNK_FNBER")
    private String sadBnkFnber;

    @Column("SAD_DEF_PAY")
    private String sadDefPay;

    @Column("SAD_WHS_COD")
    private String sadWhsCod;

    @Column("SAD_WHS_TIME")
    private BigDecimal sadWhsTime;

    @Column("SAD_TOTAL_SDI")
    private BigDecimal sadTotalSdi;

    @Column("SAD_ASMT_SERIAL")
    private String sadAsmtSerial;

    @Column("SAD_ASMT_NBER")
    private BigDecimal sadAsmtNber;

    @Column("SAD_ASMT_DATE")
    private LocalDate sadAsmtDate;

    @Column("SAD_RCPT_SERIAL")
    private String sadRcptSerial;

    @Column("SAD_RCPT_NBER")
    private BigDecimal sadRcptNber;

    @Column("SAD_RCPT_DATE")
    private LocalDate sadRcptDate;

    @Column("SAD_GRTY_AMOUNT")
    private BigDecimal sadGrtyAmount;

    @Column("SAD_GRTY_DATE")
    private LocalDate sadGrtyDate;

    @Column("SAD_TOT_FEES")
    private BigDecimal sadTotFees;

    @Column("SAD_TOTAL_TAXES")
    private BigDecimal sadTotalTaxes;

    @Column("SAD_CTRL_RESULTS")
    private String sadCtrlResults;

    @Column("SAD_DEC_PLACE")
    private String sadDecPlace;

    @Column("SAD_DEC_DATE")
    private LocalDate sadDecDate;

    @Column("SAD_DEC_REPNAM")
    private String sadDecRepnam;

    @Column("SAD_COF_NAM")
    private String sadCofNam;

    @Column("SAD_NOT_VAL")
    private BigDecimal sadNotVal;

    @Column("SAD_STA")
    private String sadSta;

    @Column("SAD_TO_BE_PAID")
    private BigDecimal sadToBePaid;

    @Column("SAD_REG_YEAR")
    private String sadRegYear;

    @Column("SAD_ASMT_YEAR")
    private String sadAsmtYear;

    @Column("SAD_STAT_VAL")
    private BigDecimal sadStatVal;

    @Column("LST_OPE")
    private String lstOpe;

    @Column("SAD_PST_NUM")
    private BigDecimal sadPstNum;

    @Column("SAD_PST_DAT")
    private LocalDate sadPstDat;
    
    @Transient
    private List<SadItm> sadItmList;

    @Transient
    private UnCmpTab unCmpTabExp;

    @Transient
    private UnCmpTab unCmpTabDest;

    @Transient
    private UnCtyTab unCtyTabExp;

    @Transient
    private UnCtyTab unCtyTabDest;
    
    @Transient
    private UnCuoTab unCuoTab;
    
    @Transient
    private UnDecTab unDecTab;
    
    @Transient
    private UnLocTab unLocTab;
    
    @Transient
    private UnTopTab unTopTab;
    
    @Transient
    private UnRatTab unRatTab;
    
    @Transient
    private SadOccCns sadOccCns;
    
    @Transient
    private SadOccExp sadOccExp;

    public void addSadItm(SadItm sadItm) {
        if (sadItmList == null) sadItmList = new ArrayList<>();
        sadItmList.add(sadItm);
    }

    // Getters and setters
}
