package bj.douanes.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.facade.SanctionFacade;
import bj.douanes.facade.UTILS.DTO;
import bj.douanes.utils.AppResponse;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("api/agents/sanction")
public class Sanction {

    @Autowired
    private SanctionFacade sanctionFacade;

    @GetMapping
    public ResponseEntity<?> getAllSanctions() {
        return AppResponse.ok(sanctionFacade.getSanctionsAllList());
    }

    @GetMapping("{id}")
    public ResponseEntity<?> getSanctionById(@PathVariable Long id) {
        return AppResponse.ok(sanctionFacade.getSanctionById(id));
    }

    @PostMapping("/{agentId}")
    public ResponseEntity<?> createLangue(@RequestBody DTO.SanctionDto sanctionDto, @PathVariable Long agentId) {
        return AppResponse.created(sanctionFacade.createSanctions(agentId, sanctionDto));
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<?> updatedLangue(@RequestBody DTO.SanctionDto sanctionDto,@PathVariable Long id){
        return AppResponse.ok(sanctionFacade.update(id, sanctionDto)); 
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<?> deleteNote(@PathVariable Long id){
        return AppResponse.ok(sanctionFacade.deleteSanctions(id)); 
    }
}
