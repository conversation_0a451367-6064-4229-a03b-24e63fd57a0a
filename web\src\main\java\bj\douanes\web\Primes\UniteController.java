package bj.douanes.web.Primes;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.DTO.UniteDTO;
import bj.douanes.facade.Prime.UniteFacade;
import bj.douanes.utils.AppResponse;
import lombok.RequiredArgsConstructor;


@RestController
@RequiredArgsConstructor
@RequestMapping("api/prime/unite")
public class UniteController {
    
    private final UniteFacade uniteFacade;

    @GetMapping("/all")
    public ResponseEntity<?> getAllUnites() {
        return AppResponse.ok(uniteFacade.getAllUnites());
    }
    @PostMapping("/create")
    public ResponseEntity<?> createUnite(@RequestBody UniteDTO uniteDTO) {
        return AppResponse.created(uniteFacade.createUnite(uniteDTO));
    }
    @PutMapping("/update/{codeUnite}")
    public ResponseEntity<?> updateUnite(@PathVariable String codeUnite, @RequestBody UniteDTO uniteDTO) {
        return AppResponse.ok(uniteFacade.updateUnite(codeUnite, uniteDTO));
    }
    @DeleteMapping("/delete/{codeUnite}")
    public ResponseEntity<?> deleteUnite(@PathVariable String codeUnite) {
        uniteFacade.deleteUnite(codeUnite);
        return AppResponse.noContent();
    }
}
