// package bj.douanes.web;

// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.DeleteMapping;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.PostMapping;
// import org.springframework.web.bind.annotation.PutMapping;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RestController;

// import bj.douanes.facade.FinPositionFacade;
// import bj.douanes.facade.UTILS.DTO;
// import bj.douanes.utils.AppResponse;
// import lombok.RequiredArgsConstructor;

// @RestController
// @RequiredArgsConstructor
// @RequestMapping("api/agents/finPosition")
// public class FinPositionController {
    
//     @Autowired
//     private FinPositionFacade finPositionFacade;

//     @GetMapping
//     public ResponseEntity<?> getAllFinPosition() {
//         return AppResponse.ok(finPositionFacade.getFinPositionAllList());
//     }

//     @GetMapping("{id}")
//     public ResponseEntity<?> getFinPositionById(@PathVariable Long id) {
//         return AppResponse.ok(finPositionFacade.getFinPositionById(id));
//     }

//     @PostMapping("/{agentId}")
//     public ResponseEntity<?> createFinPosition(@RequestBody DTO.FinPositionDto finPositionDto, @PathVariable Long agentId) {
//         return AppResponse.created(finPositionFacade.createFinPosition(agentId, finPositionDto));
//     }

//     @PutMapping("/update/{id}")
//     public ResponseEntity<?> updatedFinPosition(@RequestBody DTO.FinPositionDto finPositionDto,@PathVariable Long id){
//         return AppResponse.ok(finPositionFacade.update(id, finPositionDto)); 
//     }

//     @DeleteMapping("/delete/{id}")
//     public ResponseEntity<?> deleteFinPosition(@PathVariable Long id){
//         return AppResponse.ok(finPositionFacade.deleteFinPosition(id)); 
//     }
// }
