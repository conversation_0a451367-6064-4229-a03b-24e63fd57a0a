package bj.douanes.web.controller;

import bj.douanes.core.service.DatabaseRoutingTestService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Contrôleur de test pour démontrer le routage automatique des datasources
 */
@RestController
@RequestMapping("/api/test/database-routing")
@RequiredArgsConstructor
public class DatabaseRoutingTestController {
    
    private final DatabaseRoutingTestService testService;
    
    /**
     * Test du routage automatique basé sur le package
     */
    @GetMapping("/auto")
    public String testAutoRouting() {
        testService.testCoreServiceRouting();
        return "Test du routage automatique exécuté - vérifiez les logs pour voir la datasource utilisée";
    }
    
    /**
     * Test du routage explicite avec annotation
     */
    @GetMapping("/explicit")
    public String testExplicitRouting() {
        testService.testExplicitRouting();
        return "Test du routage explicite exécuté - vérifiez les logs pour voir la datasource utilisée";
    }
}
