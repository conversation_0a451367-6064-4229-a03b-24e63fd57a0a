package bj.douanes.sydoniaplusplus.service;

import java.util.Optional;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Consumer;

import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

import bj.douanes.sydoniaplusplus.repository.*;
import bj.douanes.sydoniaplusplus.model.*;
import bj.douanes.sydoniaplusplus.dto.*;

public interface Services {
    List<SadGen> findFirstTenDec();

    List<SadInf> findBySadInf(String chassis);

    List<SadItm> findBySadItmList(String chassis);

    Set<SadInFItmDto> findByChassisList(String chassis);

    List<SadInf> findBykeys(String keyYear, String keyCuo, String keyDec, String keyNber);

    List<SadGen> findDecBy(String year, String cuo, String dec, String nber);

    List<SadGen> findDecBySerial(String year, String cuo, String serial, String nber);

    List<SadGen> findDecBySerial2(String year, String cuo, String serial, String nber);

    List<CshGenCdi> findCshGen(String rcpYear, String rcpCuo, String rcpSerial, String rcpNber);
}

@Service
@RequiredArgsConstructor
class ServicesImpl implements Services {

    private final UnMopTabRepo unMopTabRepo;
    private final SadInfRepo sadInfRepo;
    private final CshGenCdiRepo cshGenCdiRepo;
    private final CshDecRepo cshDecRepo;
    private final CshMeaRepo cshMeaRepo;
    private final SadGenRepo sadGenRepo;
    private final SadItmRepo sadItmRepo;
    private final SadTaxRepo sadTaxRepo;
    private final UnCmpTabRepo unCmpTabRepo;
    private final UnCtyTabRepo unCtyTabRepo;
    private final UnCuoTabRepo unCuoTabRepo;
    private final UnDecTabRepo unDecTabRepo;
    private final UnLocTabRepo unLocTabRepo;
    private final UnPkgTabRepo unPkgTabRepo;
    private final UnTopTabRepo unTopTabRepo;
    private final UnRatTabRepo unRatTabRepo;
    private final SadOccExpRepo sadOccExpRepo;
    private final SadOccCnsRepo sadOccCnsRepo;

    @Override
    public List<SadGen> findFirstTenDec() {
        List<SadGen> list = sadGenRepo.findFirst10();
        list.forEach(sadgen -> {
            SadAllKeys keys = SadAllKeys.builder()
                    .keyYear(sadgen.getKeyYear())
                    .keyCuo(sadgen.getKeyCuo())
                    .keyDec(sadgen.getKeyDec())
                    .keyNber(sadgen.getKeyNber())
                    .sadNum(sadgen.getSadNum())
                    .build();

            sadgen.setSadItmList(sadItmRepo.findAllByKeys(keys));

            sadgen.getSadItmList().forEach(saditm -> {
                keys.setItmNber(saditm.getItmNber());
                saditm.setSadTaxList(sadTaxRepo.findAllByKeys(keys));
            });
        });
        return list;
    }

    @Override
    public List<SadGen> findDecBy(String year, String cuo, String dec, String nber) {
        List<SadGen> list = sadGenRepo.findByValuesDec(year, cuo, dec, nber);
        searchDec(list);

        return list;
    }

    @Override
    public List<SadInf> findBySadInf(String chassis) {
        return sadInfRepo.findBySadInfList(chassis);
    }

    @Override
    public List<SadItm> findBySadItmList(String chassis) {
        return sadItmRepo.findBySadItmList(chassis);
    }

    @Override
    public List<SadGen> findDecBySerial(String year, String cuo, String serial, String nber) {
        List<SadGen> list = sadGenRepo.findByValuesSerial(year, cuo, serial, nber);
        searchDec(list);

        return list;

    }

    @Override
    public List<SadGen> findDecBySerial2(String year, String cuo, String serial, String nber) {
        List<SadGen> list = sadGenRepo.findByValuesSerial2(year, cuo, serial, nber);
        searchDec(list);

        return list;
    }

    @Override
    public List<CshGenCdi> findCshGen(String rcpYear, String rcpCuo, String rcpSerial, String rcpNber) {
        List<CshGenCdi> liste = cshGenCdiRepo.findByCshList(rcpYear, rcpCuo, rcpSerial, rcpNber);
        findCsh(liste);

        return liste;
    }

    @Override
    public List<SadInf> findBykeys(String keyYear, String keyCuo, String keyDec, String keyNber) {
        return sadInfRepo.findByFourKeys(keyYear, keyCuo, keyDec, keyNber);
    }

    private void findCsh(List<CshGenCdi> newList) {
        newList.forEach(findCshConnsummer());
    }

    private Consumer<? super CshGenCdi> findCshConnsummer() {
        return cshGenCdi -> {
            // Chercher la liste de CshDec
            CshAllKeys keys2 = CshAllKeys.builder()
                    .rcpYear(cshGenCdi.getRcpYear())
                    .rcpCuo(cshGenCdi.getRcpCuo())
                    .rcpSerial(cshGenCdi.getRcpSerial())
                    .rcpNber(cshGenCdi.getRcpNber())

                    .build();
            // Trouver UNCMPTAB une table de reference
            if (cshGenCdi.getCdiCompany() != null)
                cshGenCdi.setUnCmpTab(unCmpTabRepo.findById(cshGenCdi.getCdiCompany()).orElse(null));

            // Chercher tout les elements de CSH_DEC
            cshGenCdi.setCshDecList(cshDecRepo.findCshAllByKeys(keys2));

            // Chercher Tout les elements CSH_MEA
            cshGenCdi.setCshMeaList(cshMeaRepo.findCshMeaAllByKeys(keys2));

            if (cshGenCdi.getCshDecList() != null) {
                cshGenCdi.getCshDecList().forEach(cshDec -> {
                    SadGen sadgen = sadGenRepo.findByValues(cshDec.getRcpYear(), cshDec.getRcpCuo(),
                            cshDec.getRcpSerial(), cshDec.getRcpNber()).orElse(null);
                    if (sadgen != null) {
                        // Find UnDecTab
                        if (sadgen.getKeyDec() != null)
                            sadgen.setUnDecTab(unDecTabRepo.findById(sadgen.getKeyDec()).orElse(null));
                        cshDec.setSadGen(sadgen);
                    }
                });
            }

            // Find UnCuoTab
            if (cshGenCdi.getRcpCuo() != null)
                cshGenCdi.setUnCuoTab(unCuoTabRepo.findById(cshGenCdi.getRcpCuo()).orElse(null));

            if (cshGenCdi.getCshMeaList() != null) {
                cshGenCdi.getCshMeaList().forEach(cshMea -> {
                    cshMea.setUnMopTab(unMopTabRepo.findById(cshMea.getCashMeanCod()).orElse(null));
                });
            }
        };
    }

    private void searchDec(List<SadGen> list) {
        list.forEach(searchDecConsumer());

    }

    private Consumer<? super SadGen> searchDecConsumer() {
        return sadgen -> {
            // Find All SadItmList
            SadAllKeys keys = SadAllKeys.builder()
                    .keyYear(sadgen.getKeyYear())
                    .keyCuo(sadgen.getKeyCuo())
                    .keyDec(sadgen.getKeyDec())
                    .keyNber(sadgen.getKeyNber())
                    .sadNum(sadgen.getSadNum())
                    .build();

            sadgen.setSadItmList(sadItmRepo.findAllByKeys(keys));

            // Find All SadTaxList of each SadItmList
            sadgen.getSadItmList().forEach(saditm -> {
                keys.setItmNber(saditm.getItmNber());
                saditm.setSadTaxList(sadTaxRepo.findAllByKeys(keys));
                // Find UnPkgTab
                saditm.setUnPkgTab(unPkgTabRepo.findById(saditm.getSaditmPackKndcod()).orElse(null));
            });

            // Verify TypDec
            if (sadgen.getSadTypDec().equalsIgnoreCase("IM")) {

                // Find SadOccExp
                sadgen.setSadOccExp(sadOccExpRepo.findByKeys(keys).orElse(null));

            } else if (sadgen.getSadTypDec().equalsIgnoreCase("EX")) {

                // Find SadOccCns
                sadgen.setSadOccCns(sadOccCnsRepo.findByKeys(keys).orElse(null));

            }

            // Find UnCmpTab
            if (sadgen.getSadConsignee() != null)
                sadgen.setUnCmpTabDest(unCmpTabRepo.findById(sadgen.getSadConsignee()).orElse(null));
            if (sadgen.getSadExporter() != null)
                sadgen.setUnCmpTabExp(unCmpTabRepo.findById(sadgen.getSadExporter()).orElse(null));

            // Find UnCtyTab
            if (sadgen.getSadCtyDestcod() != null)
                sadgen.setUnCtyTabDest(unCtyTabRepo.findById(sadgen.getSadCtyDestcod()).orElse(null));
            if (sadgen.getSadCtyExpcod() != null)
                sadgen.setUnCtyTabExp(unCtyTabRepo.findById(sadgen.getSadCtyExpcod()).orElse(null));

            // Find UnCuoTab
            if (sadgen.getKeyCuo() != null)
                sadgen.setUnCuoTab(unCuoTabRepo.findById(sadgen.getKeyCuo()).orElse(null));

            // Find UnDecTab
            if (sadgen.getKeyDec() != null)
                sadgen.setUnDecTab(unDecTabRepo.findById(sadgen.getKeyDec()).orElse(null));

            // Find UnLocTab
            if (sadgen.getSadLopCod() != null)
                sadgen.setUnLocTab(unLocTabRepo.findById(sadgen.getSadLopCod()).orElse(null));

            // Find UnTopTab

            if (sadgen.getSadTopCod() != null)
                sadgen.setUnTopTab(unTopTabRepo.findById(sadgen.getSadTopCod()).orElse(null));

            // Find UnRatTab
            if (sadgen.getSadCurCod() != null && sadgen.getSadRegDate() != null)
                sadgen.setUnRatTab(
                        unRatTabRepo.findByCodAndDate(sadgen.getSadCurCod(), sadgen.getSadRegDate()).orElse(null));

        };
    }

    @Override
    public Set<SadInFItmDto> findByChassisList(String chassis) {
        Set<SadInFItmDto> liste = new HashSet<>();
        Set<SadInFItmDto> listeFinal = new HashSet<>();

        this.findBySadItmList(chassis).forEach(sadItm -> liste.add(SadInFItmDto.builder()
                .keyCuo(sadItm.getKeyCuo())
                .keyYear(sadItm.getKeyYear())
                .keyDec(sadItm.getKeyDec())
                .keyNber(sadItm.getKeyNber())
                .build()));
        this.findBySadInf(chassis).forEach(sadInf -> liste.add(SadInFItmDto.builder()
                .keyCuo(sadInf.getKeyCuo())
                .keyYear(sadInf.getKeyYear())
                .keyDec(sadInf.getKeyDec())
                .keyNber(sadInf.getKeyNber())
                .build()));

        for (SadInFItmDto sadInFItmDto : liste) {

            Optional<SadGen> sadOptional = sadGenRepo.findByValuesDec2(sadInFItmDto.getKeyYear(),
                    sadInFItmDto.getKeyCuo(), sadInFItmDto.getKeyDec(), sadInFItmDto.getKeyNber());
            if (sadOptional.isPresent()) {
                sadInFItmDto.setSadRegSerial(sadOptional.get().getSadRegSerial());
                sadInFItmDto.setSadRegNber(sadOptional.get().getSadRegNber());
                listeFinal.add(sadInFItmDto);
            }
        }

        return listeFinal;
    }

}
