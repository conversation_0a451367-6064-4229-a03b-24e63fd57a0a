package bj.douanes.sydoniaplusplus.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.*;

import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "OPS$ASY", name = "UNCMPTAB")
public class UnCmpTab {

    @Column("CMP_COD") @Id
    private String cmpCod;

    @Column("CMP_NAM")
    private String cmpNam;

    @Column("CMP_ADR")
    private String cmpAdr;

    @Column("CMP_AD2")
    private String cmpAd2;

}
