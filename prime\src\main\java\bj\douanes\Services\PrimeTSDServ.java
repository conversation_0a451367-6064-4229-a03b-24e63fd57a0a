// package bj.douanes.Services;

// import org.springframework.stereotype.Service;

// import bj.douanes.Model.PrimeTSD;
// import bj.douanes.Repository.PrimeTSDRepo;
// import lombok.RequiredArgsConstructor;

// public interface PrimeTSDServ {
//     PrimeTSD createPrimeTSD(PrimeTSD primeTSD);
// }

// @RequiredArgsConstructor
// @Service
// class InnerPrimeTSDServ implements PrimeTSDServ {

//     private final PrimeTSDRepo primeTSDRepo;

//     @Override
//     public PrimeTSD createPrimeTSD(PrimeTSD primeTSD) {
        
//     }
// }