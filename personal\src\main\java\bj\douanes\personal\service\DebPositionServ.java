package bj.douanes.personal.service;

import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Agent;
import bj.douanes.personal.model.DebPosition;
import bj.douanes.personal.repository.AgentRepo;
import bj.douanes.personal.repository.DebPositionRepo;
import lombok.RequiredArgsConstructor;

public interface DebPositionServ {

    DebPosition getDebPositionById(Long id);
    DebPosition createDebPosition(Long id,DebPosition debPosition);
    String deleteDebPosition(Long id);
    List<DebPosition> getDebPositionAllList();
}

@Service
@RequiredArgsConstructor
class DebPositionImpl implements DebPositionServ{
    
    private final AgentRepo agentRepo;
    private final DebPositionRepo debPositionRepo;

    @Override
    public DebPosition getDebPositionById(Long id) {
        return debPositionRepo.findById(id).orElse(null);
    }

    @Override
    public DebPosition createDebPosition(Long id, DebPosition debPosition) {
        Agent agent = agentRepo.findById(id).orElse(null);
        debPosition.setAgent(agent);
        return debPositionRepo.save(debPosition);
    }

    @Override
    public String deleteDebPosition(Long id) {
        debPositionRepo.deleteById(id);
        return "DEBUT POSITION SUPP";
    }

    @Override
    public List<DebPosition> getDebPositionAllList() {
       return debPositionRepo.findAll();
    }

}