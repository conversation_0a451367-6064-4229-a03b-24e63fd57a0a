package bj.douanes.web;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.facade.SydoniaplusplusFacade;
import bj.douanes.utils.AppResponse;
import bj.douanes.utils.Endpoints;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("api/asy")
public class SydoniaplusplusController implements Endpoints {

    private final SydoniaplusplusFacade facade;

    @GetMapping(TEST)
    public ResponseEntity<?> sadGen() {
        return AppResponse.ok(facade.sadGen());
    }

    // Pour rechercher une declaration par numero d'enregistrement
    @GetMapping(GENSERIAL + "/{year}/{cuo}/{serial}/{nber}")
    public ResponseEntity<?> sadGenBySerial(
            @PathVariable String year,
            @PathVariable String cuo,
            @PathVariable String serial,
            @PathVariable String nber) {
        return AppResponse.ok(
            facade.sadGenBySerial(year.toUpperCase(), cuo.toUpperCase(), serial.toUpperCase(), nber.toUpperCase())
        );
    }

    // Pour rechercher une declaration par numero de reference
    @GetMapping(GENDEC + "/{year}/{cuo}/{dec}/{nber}")
    public ResponseEntity<?> sadGenByDec(
            @PathVariable String year,
            @PathVariable String cuo,
            @PathVariable String dec,
            @PathVariable String nber) {
        return AppResponse.ok(
            facade.sadGenByDec(year.toUpperCase(), cuo.toUpperCase(), dec.toUpperCase(), nber.toUpperCase())
        );
    }

    // Endpoint pour chercher les déclaration de mise a la consommation(IM 4)
    @GetMapping(GENSERIAL2 + "/{year}/{cuo}/{serial}/{nber}")
    public ResponseEntity<?> sadGenBySerial2(
            @PathVariable String year,
            @PathVariable String cuo,
            @PathVariable String serial,
            @PathVariable String nber) {
        return AppResponse.ok(
            facade.sadGenBySerial2(year.toUpperCase(), cuo.toUpperCase(), serial.toUpperCase(), nber.toUpperCase())
        );
    }

    // Endpoint des chassis
    @GetMapping(GENCHASSIS + "/{chassis}")
    public ResponseEntity<?> sadInf(@PathVariable String chassis) {
        return AppResponse.ok(facade.sadInf(chassis.toUpperCase()));
    }

    // Endpoint pour afficher la quittance
    @GetMapping(GENCSH + "/{rcpYear}/{rcpCuo}/{rcpSerial}/{rcpNber}")
    public ResponseEntity<?> sadCshList(
            @PathVariable String rcpYear,
            @PathVariable String rcpCuo,
            @PathVariable String rcpSerial,
            @PathVariable String rcpNber) {
        return AppResponse.ok(
            facade.sadCshList(rcpYear.toUpperCase(), rcpCuo.toUpperCase(), 
                rcpSerial.toUpperCase(), rcpNber.toUpperCase()
            )
        );
    }

    // Endpoint pour trouver les informations
    @GetMapping(GENINFO + "/{keyYear}/{keyCuo}/{keyDec}/{keyNber}")
    public ResponseEntity<?> sadTxtList(
            @PathVariable String keyYear,
            @PathVariable String keyCuo,
            @PathVariable String keyDec,
            @PathVariable String keyNber) {
        return AppResponse.ok(
            facade.sadTxtList(keyYear.toUpperCase(), keyCuo.toUpperCase(), 
                keyDec.toUpperCase(), keyNber.toUpperCase()
            )
        );
    }

}