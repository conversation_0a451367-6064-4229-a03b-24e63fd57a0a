<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

	<parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.2</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
	
    <groupId>bj.douanes</groupId>
    <artifactId>backend</artifactId>
    <version>2.0</version>
    <name>ERP DOUANES</name>
    <description>ERP DOUANES Modules Project</description>

    <packaging>pom</packaging>

    <modules>
        <module>web</module>
        <module>core</module>
		<module>facade</module>
        <module>sydoniaplusplus</module>
        <module>personal</module>
        <module>transport</module>
        <module>prime</module>
    </modules>

	<properties>
		<java.version>17</java.version>
		<!-- Projects version -->
		<web.version>0.1</web.version>
		<core.version>0.1</core.version>
		<facade.version>0.1</facade.version>
		<facade.version>0.1</facade.version>
		<sydoniaplusplus.version>0.2</sydoniaplusplus.version>
		<personal.version>0.1</personal.version>
		<transport.version>0.1</transport.version>
		<prime.version>0.1</prime.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>bj.douanes</groupId>
				<artifactId>web</artifactId>
				<version>${web.version}</version>
			</dependency>
			<dependency>
				<groupId>bj.douanes</groupId>
				<artifactId>core</artifactId>
				<version>${core.version}</version>
			</dependency>
			<dependency>
				<groupId>bj.douanes</groupId>
				<artifactId>facade</artifactId>
				<version>${facade.version}</version>
			</dependency>
			<dependency>
				<groupId>bj.douanes</groupId>
				<artifactId>sydoniaplusplus</artifactId>
				<version>${sydoniaplusplus.version}</version>
			</dependency>
			<dependency>
				<groupId>bj.douanes</groupId>
				<artifactId>personal</artifactId>
				<version>${personal.version}</version>
			</dependency>
			<dependency>
				<groupId>bj.douanes</groupId>
				<artifactId>transport</artifactId>
				<version>${transport.version}</version>
			</dependency>
			<dependency>
				<groupId>bj.douanes</groupId>
				<artifactId>prime</artifactId>
				<version>${prime.version}</version>
			</dependency>

		</dependencies>
	</dependencyManagement>

    <dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
        <!-- <dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jdbc</artifactId>
		</dependency> -->
        <dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
		<!-- <dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency> -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>runtime</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.flywaydb/flyway-core -->
		<!-- <dependency>
			<groupId>org.flywaydb</groupId>
			<artifactId>flyway-core</artifactId>
			<version>4.2.0</version>
    	</dependency> -->
		<dependency>
            <groupId>org.modelmapper</groupId>
            <artifactId>modelmapper</artifactId>
            <version>3.0.0</version>
        </dependency>
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
			<version>2.6.0</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/net.sf.jasperreports/jasperreports -->
		<dependency>
			<groupId>net.sf.jasperreports</groupId>
			<artifactId>jasperreports</artifactId>
			<version>6.20.0</version>
		</dependency>

		<dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.4.1</version>
        </dependency>	
		
		

		<!-- <dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>5.1.40	</version>
		</dependency> -->
		
		<!-- <dependency>
			<groupId>io.github.cdimascio</groupId>
			<artifactId>dotenv-java</artifactId>
			<version>3.0.0</version>
		</dependency> -->
		<!-- https://mvnrepository.com/artifact/org.apache.maven/maven-core -->
		<!-- <dependency>
			<groupId>org.apache.maven</groupId>
			<artifactId>maven-core</artifactId>
			<version>4.0.0-alpha-12</version>
			<scope>runtime</scope>
			<optional>true</optional>
		</dependency> -->
    </dependencies>

</project>
