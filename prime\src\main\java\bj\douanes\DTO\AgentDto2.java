package bj.douanes.DTO;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AgentDto2 {
    private String matricule;
    private String nom;
    private String prenoms;
    private String codeUnite;
    private String codeFonction;
    private String codeBanque;
    private String rib;
    private BigDecimal montantBonification;
    private BigDecimal partGlobalReparti;
    private BigDecimal totalBrut;
    private BigDecimal totalArrondi;
    private BigDecimal montantIts;
    private BigDecimal itsArrondi;
    private BigDecimal montantNetPayer;

}
