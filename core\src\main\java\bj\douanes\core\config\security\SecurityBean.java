package bj.douanes.core.config.security;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class SecurityBean {
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration manager) throws Exception {
        return manager.getAuthenticationManager();
    }

    @Bean
    public WebMvcConfigurer webMvcConfigurer(){
        return new WebMvcConfigurer() {
            @Override
            public void addCorsMappings(CorsRegistry registry) {
                registry.addMapping("/**") //Enable CORS for all endpoints
                        .allowedMethods("*");
            }
        };
    }

    // @Bean
    // public CorsConfigurationSource corsConfigurationSource() {
    //     CorsConfiguration configuration = new CorsConfiguration();
    //     configuration.setAllowedOrigins(Arrays.asList("http://localhost:3000","http://*************:3000","http://*********:8080")); // Autoriser toutes les origines
    //     configuration.setAllowedMethods(Arrays.asList("*")); // Autoriser toutes les méthodes (GET, POST, etc.)
    //     configuration.setAllowedHeaders(Arrays.asList("*")); // Autoriser tous les en-têtes
    //     configuration.setAllowCredentials(true); // Autoriser les en-têtes d'authentification (comme les cookies)
    //     UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    //     source.registerCorsConfiguration("/**", configuration);
    //     return source;
    // }
}
