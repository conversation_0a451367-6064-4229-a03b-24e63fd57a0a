// package bj.douanes.core.shared.schedules;
// import java.io.File;
// import java.io.FileInputStream;
// import java.io.IOException;
// import java.sql.Connection;
// import java.sql.DriverManager;
// import java.sql.PreparedStatement;
// import java.sql.SQLException;
// import java.util.concurrent.Executors;
// import java.util.concurrent.ScheduledExecutorService;
// import java.util.concurrent.TimeUnit;

// import org.apache.poi.ss.usermodel.Row;
// import org.apache.poi.ss.usermodel.Sheet;
// import org.apache.poi.ss.usermodel.Workbook;
// import org.apache.poi.xssf.usermodel.XSSFWorkbook;
// import org.springframework.stereotype.Component;

// import bj.douanes.core.config.database.WithDatabase;
// import jakarta.annotation.PostConstruct;

// @Component
// public class FileTaskRule {

//     private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

//     @PostConstruct
//     public void scheduleTask() {
//         System.out.println("-------------DEBUT SCHEDULING TASK--------------------");
//         // Schedule the task to run after 5 seconds
//         scheduler.schedule(this::readExcelFile, 5, TimeUnit.SECONDS);
//     }

//     @WithDatabase
//     public void readExcelFile() {
//         String excelPath = "/home/<USER>/giiiit/erp-backend/prime/src/main/resources/static/FEVRIER2025.xlsx";
    
//         System.out.println("-------------DEBUT LECTURE DU FICHIER EXCEL--------------------");
//         System.out.println("Le fichier Excel est : " + excelPath);
//         try (Connection conn = DriverManager.getConnection("**************************************", "root", "root");
//             FileInputStream fis = new FileInputStream(new File(excelPath));
//              Workbook workbook = new XSSFWorkbook(fis)) {
//              conn.setAutoCommit(false); // désactiver le commit automatique   
//             Sheet sheet = workbook.getSheetAt(0); // première feuille
//             String sql = "INSERT INTO r_agent_repartition (id_prime_agent) VALUES (?) ON CONFLICT (id_prime_agent) DO NOTHING";
//             System.out.println("-------------DEBUT INSERTION--------------------");
//             try (PreparedStatement stmt = conn.prepareStatement(sql)) {
//                 // Préparez votre instruction SQL ici
            
            
//             // for (Row row : sheet) {
//             //     if (row.getRowNum() == 0) continue; // ignorer l'en-tête
//             //     System.out.println("Ligne: " + row.getRowNum());
//             //     if((row.getCell(0) == null || row.getCell(0).getStringCellValue().isEmpty()) || (row.getCell(1) == null || row.getCell(1).getStringCellValue().isEmpty())) {
//             //         System.out.println("Une des cellules est vide, ligne: " );
//             //         break; // stopper la boucle si une cellule est vide
//             //     }
                
                

                
//             //     stmt.setInt(1, id_prime_agent);
                
//             //     stmt.addBatch();
//             //    // System.out.println("Matricule: " + matricule + ", montant_bonification: " + montant_bonification + ", part_global_reparti: " + part_global_reparti + ", total_brut: " + total_brut + ", total_arrondi: " + total_arrondi + ", montant_its: " + montant_its + ", its_arrondi: " + its_arrondi + ", montant_netpayer: " + montant_netpayer);
//             //     //System.out.println("Code Unite: " + code_unite + ", id_repartition: " + id_repartition + ", montant_verset: " + montant_verset + ", oeuvre_social_unite: " + oeuvre_social_unite + ", bonification_unite: " + bonification_unite + ", part_unite: " + part_unite + ", cumul_coef: " + cumul_coef);
//             //     }
            
//                 stmt.executeBatch(); // exécutez le lot
//                 System.out.println("=============================>EXECUTION DU BATCHHHHHHHHHHHH<======================");
//            // stmt.executeBatch(); // exécutez le lot
           

//             System.out.println("===============================COMMIT=========================");
//             conn.commit();       // commit une seule fois
//             System.out.println("Batch insert terminé avec succès.");
//             } catch (SQLException e) {
//                 System.out.println("Erreur lors de l'insertion des données : " + e.getMessage());
//                 try {
//                     conn.rollback(); // rollback en cas d'erreur
//                 } catch (SQLException rollbackEx) {
//                     System.out.println("Erreur lors du rollback : " + rollbackEx.getMessage());
//                 }
//                 e.printStackTrace();
//             }
//         } catch (IOException e) {
//             e.printStackTrace();
//         }catch (SQLException e) {
//             e.printStackTrace();
//         }finally {
//             System.out.println("-------------FIN LECTURE DU FICHIER EXCEL--------------------");
//         }
//     }
      
// }
