package bj.douanes.facade.Prime;

import java.io.IOException;
import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.DTO.AgentCreationDto;
import bj.douanes.DTO.AgentDto;
import bj.douanes.Model.AgentTSD;
import bj.douanes.Services.AgentCreationService;
import lombok.RequiredArgsConstructor;

public interface AgentCreationFacade {

   // List<AgentCreationDto> getAllAgents();
   List<AgentDto> getAllAgentsByUnite(Long idRepartition);
    void createAgent(AgentCreationDto agentDetails);
    AgentTSD updateAgent(String matricule,AgentCreationDto agentDetails);
    byte[] downloadAgentList(Long idRepartition) throws IOException;
    byte[] downloadOrdreVirementList(Long idRepartition) throws IOException;
    // Agent getAgentById(Long id);
    // Agent updateAgent(Long id, Object agentDetails);
    // void deleteAgent(Long id);
}

@Service
@RequiredArgsConstructor
class InnerAgentCreationFacade implements AgentCreationFacade {

    private final AgentCreationService agentCreationService;

    @Override
    public List<AgentDto> getAllAgentsByUnite(Long idRepartition) {
        return agentCreationService.getAllAgentDoRepartPrime(idRepartition);
    }

    @Override
    public void createAgent(AgentCreationDto agentDetails) {
        agentCreationService.createAgentFromDto(agentDetails);
    }

    @Override
    public AgentTSD updateAgent(String matricule, AgentCreationDto agentDetails) {
        return agentCreationService.updateAgentFromDto(matricule, agentDetails);
    }
    
    @Override
    public byte[] downloadAgentList(Long idRepartition) throws IOException{
        return agentCreationService.downloadAgentsExcel(idRepartition);
    }

    @Override
    public byte[] downloadOrdreVirementList(Long idRepartition) throws IOException {
        return agentCreationService.downloadOrdreVirementExcel(idRepartition);
    }
}