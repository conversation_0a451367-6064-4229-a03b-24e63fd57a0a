package bj.douanes.sydoniaplusplus.repository;

import java.util.List;

import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.ListCrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import bj.douanes.sydoniaplusplus.model.SadItm;
import bj.douanes.sydoniaplusplus.model.SadAllKeys;

@Repository
public interface SadItmRepo extends ListCrudRepository<SadItm, SadAllKeys> {

    @Query("SELECT * FROM \"OPS$ASY\".SAD_ITM WHERE ROWNUM <= 10")
    List<SadItm> findFirst10();

    @Query("SELECT * FROM \"OPS$ASY\".SAD_ITM WHERE key_year = :#{#keys.keyYear} AND key_cuo = :#{#keys.keyCuo} AND key_dec = :#{#keys.keyDec} AND key_nber = :#{#keys.keyNber} AND sad_num = :#{#keys.sadNum}")
    List<SadItm> findAllByKeys(SadAllKeys keys);

    @Query("SELECT * FROM \"OPS$ASY\".SAD_ITM WHERE SADITM_PACK_MARK1 LIKE '%' || :chassis || '%'" +
            " OR SADITM_PACK_MARK2 LIKE '%' || :chassis || '%'" +
            " OR SADITM_GOODS_DESC1 LIKE '%' || :chassis || '%'" +
            " OR SADITM_GOODS_DESC2 LIKE '%' || :chassis || '%'" +
            " OR SADITM_GOODS_DESC3 LIKE '%' || :chassis || '%'" +
            " OR SADITM_CTNR_NBER1 LIKE '%' || :chassis || '%'" +
            " OR SADITM_CTNR_NBER2 LIKE '%' || :chassis || '%'" +
            " OR SADITM_CTNR_NBER3 LIKE '%' || :chassis || '%'" +
            " OR SADITM_CTNR_NBER4 LIKE '%' || :chassis || '%'" +
            " OR SADITM_TRSP_DOC LIKE '%' || :chassis || '%'")
    List<SadItm> findBySadItmList(@Param("chassis") String chassis);

}
