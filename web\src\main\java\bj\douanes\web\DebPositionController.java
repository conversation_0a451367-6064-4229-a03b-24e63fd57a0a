package bj.douanes.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.facade.DebPositionFacade;
import bj.douanes.facade.UTILS.DTO;
import bj.douanes.utils.AppResponse;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("api/agents/debPosition")
public class DebPositionController {
    
    @Autowired
    private DebPositionFacade debPositionFacade;

    @GetMapping
    public ResponseEntity<?> getAllDebPosition() {
        return AppResponse.ok(debPositionFacade.getdebPositionAllList());
    }

    @GetMapping("{id}")
    public ResponseEntity<?> getDebPositionById(@PathVariable Long id) {
        return AppResponse.ok(debPositionFacade.getdebPositionById(id));
    }

    @PostMapping("/{agentId}")
    public ResponseEntity<?> createDebPosition(@RequestBody DTO.DebPositionDto debPositionDto, @PathVariable Long agentId) {
        return AppResponse.created(debPositionFacade.createdebPosition(agentId, debPositionDto));
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<?> updated(@RequestBody DTO.DebPositionDto debPositionDto,@PathVariable Long id){
        return AppResponse.ok(debPositionFacade.update(id, debPositionDto)); 
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<?> deleteDebPosition(@PathVariable Long id){
        return AppResponse.ok(debPositionFacade.deletedebPosition(id)); 
    }
}
