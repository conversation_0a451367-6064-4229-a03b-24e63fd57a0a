package bj.douanes.facade;

import java.util.List;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Actage;
import bj.douanes.personal.service.ActServ;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface ActFacade {
    
    Actage getActageById(Long id);
    Actage createActage(Long agentId,Object actage);
    String deleteActage(Long id);
    List<Actage> getActageAllList();
    Actage update(Long id,Object actage);
}

@Slf4j
@Service
@RequiredArgsConstructor
class ActFacadeImpl implements ActFacade{

    private final ModelMapper modelMapper;
    private final ActServ actServ;
    
    @Override
    public Actage getActageById(Long id) {
        return actServ.getActageById(id);
    }

    @Override
    public Actage createActage(Long agentId,Object actage) {
        var newAct = modelMapper.map(actage, Actage.class);
        return actServ.createActage(agentId, newAct);
    }

    @Override
    public String deleteActage(Long id) {
        return actServ.deleteActage(id);
    }

    @Override
    public List<Actage> getActageAllList() {
       return actServ.getActageAllList();
    }

    @Override
    public Actage update(Long id, Object actage) {
        log.info("Implementation de la mise a jour");
        Actage actage2 = actServ.getActageById(id);
        modelMapper.map(actage, actage2);
        log.info("Implementation de la mise a jour",actage2 );
        return actServ.createActage(id, actage2);
    }
    
}