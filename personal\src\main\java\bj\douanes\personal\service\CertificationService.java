package bj.douanes.personal.service;

import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Agent;
import bj.douanes.personal.model.Certification;
import bj.douanes.personal.repository.AgentRepo;
import bj.douanes.personal.repository.CertificationRepo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface CertificationService {

    Certification getCertifById(Long id);
    Certification createCertif(Long id,Certification certification);
    String deleteCertif(Long id);
    List<Certification> getCertifAllList();
    
}

@Slf4j
@Service
@RequiredArgsConstructor
class CertifServImpl implements CertificationService{

    private final CertificationRepo certificationRepo;
    private final AgentRepo agentRepo;

    @Override
    public Certification getCertifById(Long id) {
       return certificationRepo.findById(id).orElse(null);
    }

    @Override
    public Certification createCertif(Long id, Certification certification) {
        Agent agent = agentRepo.findById(id).orElse(null);
        certification.setAgent(agent);
        return certificationRepo.save(certification);
    }

    @Override
    public String deleteCertif(Long id) {
       certificationRepo.deleteById(id);
       return "Diplome supprimer";
    }

    @Override
    public List<Certification> getCertifAllList() {
       return certificationRepo.findAll();
    }

    
}