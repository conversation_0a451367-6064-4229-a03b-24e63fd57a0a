server.port=8082

app.datasource.type.names=primary

#Primary DB
#postgres database configuration
app.datasource.primary.url=****************************************
app.datasource.primary.username=root
app.datasource.primary.password=rootoor
app.datasource.primary.driver-class-name=org.postgresql.Driver
app.datasource.primary.dialect=org.hibernate.dialect.PostgreSQLDialect

# Configuration du mapping package -> datasource
app.datasource.mapping.default-data-source=primary
app.datasource.mapping.packages.bj.douanes.personal=primary
app.datasource.mapping.packages.bj.douanes.transport=primary
app.datasource.mapping.packages.bj.douanes.prime=primary
app.datasource.mapping.packages.bj.douanes.sydoniaplusplus=primary
app.datasource.mapping.packages.bj.douanes.core=primary
