package bj.douanes.facade;

import java.util.List;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Langues;
import bj.douanes.personal.service.LangueServ;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface LangueFacade {

    Langues getLanguesById(Long id);
    Langues createLangues(Long agentId,Object langue);
    String deleteLangues(Long id);
    List<Langues> getLanguesAllList();
    Langues update(Long id,Object langue);
    
}

@Slf4j
@Service
@RequiredArgsConstructor
class LangueFacadeImpl implements LangueFacade{

    private final ModelMapper modelMapper;
    private final LangueServ langueServ;

    @Override
    public Langues getLanguesById(Long id) {
        return langueServ.getLanguesById(id);
    }

    @Override
    public Langues createLangues(Long agentId, Object langue) {
        var newLangue = modelMapper.map(langue, Langues.class);
        return langueServ.createLangues(agentId, newLangue);
    }

    @Override
    public String deleteLangues(Long id) {
       return langueServ.deleteLangues(id);
    }

    @Override
    public List<Langues> getLanguesAllList() {
        return langueServ.getLanguesAllList();
    }

    @Override
    public Langues update(Long id, Object langue) {
        log.info("Implementation de la mise a jour");
        Langues langues = langueServ.getLanguesById(id);
        modelMapper.map(langue, langues);
        return langueServ.createLangues(id, langues);
    }
    
}