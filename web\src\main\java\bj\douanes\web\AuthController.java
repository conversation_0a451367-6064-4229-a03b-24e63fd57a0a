package bj.douanes.web;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.facade.AuthFacade;
import bj.douanes.facade.UTILS.DTO;
import bj.douanes.utils.AppResponse;
import bj.douanes.utils.Endpoints;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("api/auth")
public class AuthController implements Endpoints {

    private final AuthFacade facade;

    @PostMapping(SIGNUP)
    public ResponseEntity<?> makeSignup(@Valid @RequestBody DTO.AuthRequestSignup signupReq) {
        return AppResponse.created(facade.signUp(signupReq));
    }

    @PostMapping(SIGNIN)
    public ResponseEntity<?> makeSignin(@Valid @RequestBody DTO.AuthRequestSignin signinReq) {
        return AppResponse.ok(facade.signIn(signinReq));
    }
}