package bj.douanes.core.shared.error;

import lombok.extern.slf4j.Slf4j;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import bj.douanes.core.shared.dto.Field;
import bj.douanes.core.shared.dto.AppError;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.security.SignatureException;

import java.util.List;
import java.util.Objects;

import static org.springframework.web.context.request.RequestAttributes.SCOPE_REQUEST;
import static org.springframework.web.servlet.HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE;

@Slf4j
@RestControllerAdvice
public class RestExceptionHandler {

        @ExceptionHandler(MethodArgumentNotValidException.class)
        @ResponseStatus(HttpStatus.NOT_ACCEPTABLE)
        public ResponseEntity<AppError> handleValidationException(WebRequest webRequest,
                        MethodArgumentNotValidException ex) {

                Throwable throwable = ex.fillInStackTrace();
                final HttpStatus status = HttpStatus.NOT_ACCEPTABLE;
                final String message = "Arguments Not Valid";
                final List<Field> fields = ex.getBindingResult().getFieldErrors()
                                .stream()
                                .map(fe -> new Field(
                                                fe.getDefaultMessage(),
                                                fe.getField(),
                                                fe.getRejectedValue(),
                                                fe.isBindingFailure(),
                                                fe.getCode()))
                                .toList();

                AppError appError = new AppError();
                appError.setError(message);
                appError.setStatusCode(status);
                appError.setFieldErrors(fields);
                log.error(
                        String.format("STATUS: %s, MESSAGE: %s, PATH: %s", status, ex.getMessage(),
                                        webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE,SCOPE_REQUEST)
                        )
                );
                return new ResponseEntity<>(appError, status);
        }

        @ExceptionHandler(ApiException.class)
        public ResponseEntity<AppError> handleApiException(WebRequest webRequest, ApiException ex) {

                Throwable throwable = ex.getThrowable();
                final HttpStatus status = ex.getStatus();
                final String message = (!ex.getMessage().isBlank()) ? ex.getMessage()
                                : "Message not provided for this ApiException.";

                AppError appError = new AppError();
                appError.setError(message);
                appError.setStatusCode(status);
                log.error(
                        String.format("STATUS: %s, MESSAGE: %s, PATH: %s", status, message,
                                        webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, SCOPE_REQUEST)
                        ),
                        throwable
                );
                return new ResponseEntity<>(appError, status);
        }

        // General Exception
        @ExceptionHandler(Exception.class)
        public ResponseEntity<AppError> handleAllOtherException(WebRequest webRequest, Exception ex) {

                Throwable throwable = ex.fillInStackTrace();
                HttpStatus status = HttpStatus.valueOf(500);
                String message = Objects.nonNull(ex.getMessage()) ? ex.getMessage()
                                : "Message not provided for this Exception.";

                //
                if (ex instanceof AccessDeniedException || 
                                ex instanceof io.jsonwebtoken.ExpiredJwtException) {
                        status = HttpStatus.valueOf(403);
                }
                if (ex instanceof SignatureException ||
                                ex instanceof ExpiredJwtException) {
                        status = HttpStatus.valueOf(401);
                        // message = "Invalide JWT Signature.";
                }
                if (ex instanceof BadCredentialsException) {
                        status = HttpStatus.valueOf(400);
                }

                AppError appError = new AppError();
                appError.setError(message);
                appError.setStatusCode(status);
                log.error(
                        String.format("STATUS: %s, MESSAGE: %s, PATH: %s", status, message,
                                webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, SCOPE_REQUEST)
                        ),
                        throwable
                );
                return new ResponseEntity<>(appError, status);
        }
}
