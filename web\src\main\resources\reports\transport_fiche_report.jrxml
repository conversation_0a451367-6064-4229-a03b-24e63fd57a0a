<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.21.2.final using JasperReports Library version 3.5.3  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="transport_fiche_report" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<queryString language="SQL">
		<![CDATA[]]>
	</queryString>
	<field name="cuoCode" class="java.lang.String"/>
	<field name="produit" class="java.lang.String"/>
	<field name="numCamion" class="java.lang.String"/>
	<field name="nomConducteur" class="java.lang.String"/>
	<field name="prenomConducteur" class="java.lang.String"/>
	<field name="numPermisConducteur" class="java.lang.String"/>
	<field name="contact" class="java.lang.String"/>
	<field name="nameOwner" class="java.lang.String"/>
	<field name="firstnameOwner" class="java.lang.String"/>
	<field name="contactOwner" class="java.lang.String"/>
	<field name="nombreSac100" class="java.lang.Integer"/>
	<field name="nombreSac50" class="java.lang.Integer"/>
	<field name="poidsProduct" class="java.lang.Integer"/>
	<field name="communeProduct" class="java.lang.String"/>
	<field name="destinationProduct" class="java.lang.String"/>
	<field name="dateAsDate" class="java.util.Date"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="36" splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="47" splitType="Stretch">
			<image>
				<reportElement x="40" y="11" width="140" height="25"/>
				<imageExpression class="java.lang.String"><![CDATA["https://finances.bj/wp-content/themes/theme-ministere-finances/img/icons/official_logo.png"]]></imageExpression>
			</image>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="61" splitType="Stretch">
			<staticText>
				<reportElement x="100" y="7" width="261" height="21"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[DIRECTION GENERALES DES DOUANES]]></text>
			</staticText>
			<staticText>
				<reportElement x="30" y="29" width="520" height="21"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[FICHE DE RENSEIGNEMENTS ET DE CONVOYAGE DES PRODUITS AGRICOLES]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="223" splitType="Stretch">
			<staticText>
				<reportElement x="12" y="6" width="100" height="14"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[Service douane:]]></text>
			</staticText>
			<staticText>
				<reportElement x="11" y="25" width="100" height="14"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[Produit charger]]></text>
			</staticText>
			<staticText>
				<reportElement x="10" y="43" width="100" height="14"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[N° camion]]></text>
			</staticText>
			<staticText>
				<reportElement x="11" y="60" width="139" height="14"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[Nom et  prenom du chauffeur]]></text>
			</staticText>
			<staticText>
				<reportElement x="11" y="81" width="100" height="14"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[Contact]]></text>
			</staticText>
			<staticText>
				<reportElement x="11" y="100" width="100" height="14"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[Permis de conduire]]></text>
			</staticText>
			<staticText>
				<reportElement x="12" y="117" width="148" height="14"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[Nom et prenoms producteur]]></text>
			</staticText>
			<staticText>
				<reportElement x="13" y="137" width="100" height="14"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[Contacts]]></text>
			</staticText>
			<staticText>
				<reportElement x="13" y="155" width="117" height="14"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[Nombre de sac et poids]]></text>
			</staticText>
			<staticText>
				<reportElement x="13" y="171" width="100" height="14"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[Venant de:]]></text>
			</staticText>
			<staticText>
				<reportElement x="189" y="191" width="31" height="14"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[Fait :]]></text>
			</staticText>
			<textField>
				<reportElement x="176" y="8" width="100" height="13">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="6a923ba6-24a2-4637-9cb1-fa5aa6e191de"/>
				</reportElement>
				<textElement lineSpacing="Single"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cuoCode}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="172" y="28" width="100" height="14">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="3c5d80c7-8b31-4fff-a1a6-c1f89ec04d4c"/>
				</reportElement>
				<textElement lineSpacing="Single"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{produit}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="169" y="44" width="100" height="15">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="d7f8cb08-bd14-4ab8-983b-1cf8986bac04"/>
				</reportElement>
				<textElement lineSpacing="Single"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{numCamion}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="176" y="62" width="100" height="17">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="7b7ab6dc-aea6-458e-a230-0aca2ac37a04"/>
				</reportElement>
				<textElement lineSpacing="Single"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomConducteur}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="354" y="62" width="100" height="17">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="89e05635-3d26-4107-8f97-9d439d2cb997"/>
				</reportElement>
				<textElement lineSpacing="Single"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{prenomConducteur}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="172" y="81" width="100" height="14">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="393a2d1e-2881-422d-be9c-1066cdfbdddc"/>
				</reportElement>
				<textElement lineSpacing="Single"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contact}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="175" y="97" width="100" height="16">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="511c25ba-5ce7-4364-861d-69f4dd7afafd"/>
				</reportElement>
				<textElement lineSpacing="Single"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{numPermisConducteur}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="175" y="112" width="100" height="21">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="c1fa1da4-9ce7-4183-ae08-ebed5c37e74c"/>
				</reportElement>
				<textElement lineSpacing="Single"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nameOwner}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="290" y="112" width="100" height="24">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="e9fe0678-d278-4dc8-b633-5d661195c657"/>
				</reportElement>
				<textElement lineSpacing="Single"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{firstnameOwner}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="175" y="136" width="100" height="19">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1dc4594c-7a2d-4b5a-9661-9e21a4ac9751"/>
				</reportElement>
				<textElement lineSpacing="Single"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contactOwner}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="171" y="157" width="100" height="13">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="078df159-25b1-4f9e-abfd-6d7deed02036"/>
				</reportElement>
				<textElement lineSpacing="Single"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nombreSac100}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="289" y="156" width="100" height="18">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="64859e7d-d53b-432d-9504-5cb39b3be2fa"/>
				</reportElement>
				<textElement lineSpacing="Single"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nombreSac50}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="147" y="172" width="100" height="16">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="fb5af73a-aa21-454f-9b98-72e7f89a570f"/>
				</reportElement>
				<textElement lineSpacing="Single"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{communeProduct}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="262" y="192" width="100" height="17">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="18c9e7db-1ecb-4005-a28a-433fd4d99d3b"/>
				</reportElement>
				<textElement lineSpacing="Single"/>
				<textFieldExpression class="java.lang.String"><![CDATA[new java.text.SimpleDateFormat("dd/MM/yyyy").format($F{dateAsDate})]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="45" splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="54" splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band height="42" splitType="Stretch"/>
	</summary>
</jasperReport>
