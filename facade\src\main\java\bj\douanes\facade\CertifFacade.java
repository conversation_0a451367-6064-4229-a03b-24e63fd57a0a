package bj.douanes.facade;

import java.util.List;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Certification;
import bj.douanes.personal.service.CertificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface CertifFacade {
    
    Certification getCertifById(Long id);
    Certification createCertif(Long id,Object certification);
    String deleteCertif(Long id);
    List<Certification> getCertifAllList();
    Certification update(Long id,Object certification);
}

@Slf4j
@Service
@RequiredArgsConstructor
class CertifFacadeImpl implements CertifFacade{
    
    private final ModelMapper modelMapper;
    private final CertificationService service;
    
    @Override
    public Certification getCertifById(Long id) {
     return service.getCertifById(id);
    }

    @Override
    public Certification createCertif(Long id, Object certification) {
        var newCertif = modelMapper.map(certification, Certification.class);
        return service.createCertif(id, newCertif);
    }

    @Override
    public String deleteCertif(Long id) {
        return service.deleteCertif(id);
    }

    @Override
    public List<Certification> getCertifAllList() {
       return service.getCertifAllList();
    }

    @Override
    public Certification update(Long id, Object certification) {
        log.info("Implementation de la mise a jour");
        Certification certification2 = service.getCertifById(id);
        modelMapper.map(certification,certification2);
        return service.createCertif(id, certification2);
    }

}