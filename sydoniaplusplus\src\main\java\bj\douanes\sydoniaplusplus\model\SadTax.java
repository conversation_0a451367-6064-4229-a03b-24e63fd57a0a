package bj.douanes.sydoniaplusplus.model;

import java.math.BigDecimal;

import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "OPS$ASY" ,name = "SAD_TAX")
public class SadTax {

    @Column("ITM_NBER") @JsonIgnore
    private BigDecimal itmNber;
    
    @Column("KEY_YEAR") @JsonIgnore
    private String keyYear;
    
    @Column("KEY_CUO") @JsonIgnore
    private String keyCuo;
    
    @Column("KEY_DEC") @JsonIgnore
    private String keyDec;
    
    @Column("KEY_NBER") @JsonIgnore
    private String keyNber;
    
    @Column("SAD_NUM") @JsonIgnore
    private BigDecimal sadNum;

    @Column("SADITM_TAX_NBER")
    private BigDecimal saditmTaxNber;

    @Column("SADITM_TAX_CODE")
    private String saditmTaxCode;

    @Column("SADITM_TAX_RATE")
    private BigDecimal saditmTaxRate;

    @Column("SADITM_TAX_AMOUNT")
    private BigDecimal saditmTaxAmount;

    @Column("SADITM_TAX_BASE")
    private BigDecimal saditmTaxBase;

    @Column("SADITM_TAX_NOEXO")
    private BigDecimal saditmTaxNoexo;

    @Column("SADITM_TAX_MOP")
    private String saditmTaxMop;

    @Column("SADITM_TAX_BTCOD")
    private String saditmTaxBtcod;

    // Getters and setters
}
