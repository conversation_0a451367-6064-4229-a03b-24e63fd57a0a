package bj.douanes.Services;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import bj.douanes.DTO.AgentDto2;

public class ExcelGenerator {
    
    public static byte[] generateExcel(List<AgentDto2> agents) throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Agents");
            int rowIdx = 0;

            // Écriture de l’en-tête
            Row header = sheet.createRow(rowIdx++);
            String[] columns = {
                "Matricule", "Nom", "Prénoms", "Unité", "Fonction", "Code Banque", "RIB",
                "Bonification", "Part Globale", "Total Brut", "Total Arrondi",
                "Montant ITS", "ITS Arrondi", "Net à payer"
            };
            for (int i = 0; i < columns.length; i++) {
                header.createCell(i).setCellValue(columns[i]);
            }

            // Écriture des données
            for (AgentDto2 agent : agents) {
                Row row = sheet.createRow(rowIdx++);
                row.createCell(0).setCellValue(agent.getMatricule());
                row.createCell(1).setCellValue(agent.getNom());
                row.createCell(2).setCellValue(agent.getPrenoms());
                row.createCell(3).setCellValue(agent.getCodeUnite());
                row.createCell(4).setCellValue(agent.getCodeFonction());
                row.createCell(5).setCellValue(agent.getCodeBanque());
                row.createCell(6).setCellValue(agent.getRib());
                row.createCell(7).setCellValue(agent.getMontantBonification() != null ? agent.getMontantBonification().doubleValue() : 0);
                row.createCell(8).setCellValue(agent.getPartGlobalReparti() != null ? agent.getPartGlobalReparti().doubleValue() : 0);
                row.createCell(9).setCellValue(agent.getTotalBrut() != null ? agent.getTotalBrut().doubleValue() : 0);
                row.createCell(10).setCellValue(agent.getTotalArrondi() != null ? agent.getTotalArrondi().doubleValue() : 0);
                row.createCell(11).setCellValue(agent.getMontantIts() != null ? agent.getMontantIts().doubleValue() : 0);
                row.createCell(12).setCellValue(agent.getItsArrondi() != null ? agent.getItsArrondi().doubleValue() : 0);
                row.createCell(13).setCellValue(agent.getMontantNetPayer() != null ? agent.getMontantNetPayer().doubleValue() : 0);
            }

            // Ajustement automatique de la largeur des colonnes
            for (int i = 0; i < columns.length; i++) {
                sheet.autoSizeColumn(i);
            }

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            return out.toByteArray();
        }
    }
}
