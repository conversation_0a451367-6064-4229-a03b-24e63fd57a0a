package bj.douanes.Services;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;

import bj.douanes.DTO.FonctionsDTO;
import bj.douanes.Model.Fonction;
import bj.douanes.Model.RFonctionTypePrime;
import bj.douanes.Model.RFonctionTypePrimeId;
import bj.douanes.Model.TypePrime;
import bj.douanes.Repository.FonctionRepository;
import bj.douanes.Repository.PrimesRepo;
import bj.douanes.Repository.RFonctionTypePrimeRepository;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

public interface FonctionServ {
    
    List<FonctionsDTO> getAllFonctions();
    RFonctionTypePrime createFonction(FonctionsDTO dto);
    FonctionsDTO updateFonction(String codeFonction, FonctionsDTO fonction);
    void deleteFonction(String codeFonction);
}

@Service
@RequiredArgsConstructor
class FonctionServImpl implements FonctionServ {

    private final FonctionRepository fonctionRepository;
    private final RFonctionTypePrimeRepository rFonctionTypePrimeRepository;
    private final PrimesRepo primesRepo;

    @Override
    public List<FonctionsDTO> getAllFonctions() {
        return rFonctionTypePrimeRepository.findAll().stream()
            .filter(rftp -> rftp.getFonction() != null && rftp.getTypePrime() != null)
            .map(rftp -> {
                FonctionsDTO dto = new FonctionsDTO();
                dto.setCodeFonction(rftp.getId().getCodeFonction());
                dto.setLibelle(rftp.getFonction().getLibelle());
                dto.setCodeTypePrime(rftp.getId().getCodeTypePrime());
                dto.setCoefficient(rftp.getCoefficient());
                dto.setDateDebut(rftp.getId().getDateDebut());
                dto.setCreatedAt(rftp.getCreatedAt());
                dto.setUpdatedAt(rftp.getUpdatedAt());
                return dto;
            })
            .toList();
    }

    @Override
    @Transactional
    public RFonctionTypePrime createFonction(FonctionsDTO dto) {
        if (dto.getCodeFonction() == null || dto.getCodeFonction().trim().isEmpty()) {
        throw new IllegalArgumentException("Le code de la fonction ne peut pas être nul.");
    }
        // 1. Vérifier si Fonction existe déjà pour éviter un doublon
        Fonction fonction = fonctionRepository.findByCodeFonction(dto.getCodeFonction());
        if (fonction != null) {
            throw new IllegalArgumentException("La fonction avec le code " + dto.getCodeFonction() + " existe déjà.");
        }
        //creer la fonction si elle n'existe pas
        fonction = new Fonction();
        fonction.setCodeFonction(dto.getCodeFonction());
        fonction.setLibelle(dto.getLibelle());
        fonction.setCreatedAt(dto.getCreatedAt() != null ? dto.getCreatedAt() : LocalDate.now());
        fonction.setUpdatedAt(dto.getUpdatedAt() != null ? dto.getUpdatedAt() : LocalDate.now());
        Fonction savedFonction = fonctionRepository.save(fonction);

        // 2. Récupérer TypePrime
        TypePrime typePrime = primesRepo.findByCodeTypePrime(dto.getCodeTypePrime());
        if (typePrime == null) {
            throw new EntityNotFoundException("Type de prime :" + dto.getCodeTypePrime()+" non trouvé ");
        }

        // 3. Construire la clé composite
        RFonctionTypePrimeId id = new RFonctionTypePrimeId();
        id.setCodeFonction(savedFonction.getCodeFonction());
        id.setCodeTypePrime(typePrime.getCodeTypePrime());
        id.setDateDebut(dto.getDateDebut() != null ? dto.getDateDebut() : LocalDate.now());

        // 4. Créer et remplir l'entité RFonctionTypePrime
        RFonctionTypePrime rftp = new RFonctionTypePrime();
        rftp.setId(id);
        rftp.setFonction(savedFonction);
        rftp.setTypePrime(typePrime);
        rftp.setCoefficient(dto.getCoefficient());
        rftp.setCreatedAt(dto.getCreatedAt() != null ? dto.getCreatedAt() : LocalDate.now());
        rftp.setUpdatedAt(dto.getUpdatedAt() != null ? dto.getUpdatedAt() : LocalDate.now());

        // 5. Sauvegarder
        return rFonctionTypePrimeRepository.save(rftp);
    }


    @Transactional
    @Override
    public FonctionsDTO updateFonction(String codeFonction, FonctionsDTO dto) {
        if (codeFonction == null || codeFonction.trim().isEmpty()) {
            throw new IllegalArgumentException("Le code de la fonction ne peut pas être nul ou vide.");
        }

        // 1. Trouver la fonction existante
        Fonction fonction = fonctionRepository.findByCodeFonction(codeFonction);
        if (fonction == null) {
            throw new EntityNotFoundException("La fonction avec le code " + codeFonction + " n'existe pas.");
        }

        // 2. Mettre à jour fonction
        fonction.setLibelle(dto.getLibelle());
        System.out.println("----->LIBELLE : " + dto.getLibelle()+" <-----");
        fonction.setUpdatedAt(LocalDate.now());
        fonctionRepository.save(fonction);        

        // 3. Trouver TypePrime
        TypePrime typePrime = primesRepo.findByCodeTypePrime(dto.getCodeTypePrime());
        if (typePrime == null) {
            throw new EntityNotFoundException("Type de prime : " + dto.getCodeTypePrime() + " non trouvé.");
        }

        RFonctionTypePrimeId id = new RFonctionTypePrimeId();
        id.setCodeFonction(codeFonction);
        id.setCodeTypePrime(dto.getCodeTypePrime());
        id.setDateDebut(dto.getDateDebut() != null ? dto.getDateDebut() : LocalDate.now());

        Optional<RFonctionTypePrime> optionalRftp = rFonctionTypePrimeRepository.findById(id);

        RFonctionTypePrime rftp;
        //si existe mettre a jour
        if (optionalRftp.isPresent()) {
            rftp = optionalRftp.get();
            rftp.setFonction(fonction); 
            rftp.setCoefficient(dto.getCoefficient());
            rftp.setUpdatedAt(LocalDate.now());
        } else {
            //créer si l'objet n’existe pas :
            rftp = new RFonctionTypePrime();
            rftp.setId(id);
            rftp.setFonction(fonction);
            rftp.setTypePrime(typePrime);
            rftp.setCoefficient(dto.getCoefficient());
            rftp.setCreatedAt(LocalDate.now());
            rftp.setUpdatedAt(LocalDate.now());
        }  
        RFonctionTypePrime saved = rFonctionTypePrimeRepository.save(rftp);

        FonctionsDTO dtoResponse = new FonctionsDTO();
        dtoResponse.setCodeFonction(saved.getFonction().getCodeFonction());
        dtoResponse.setLibelle(saved.getFonction().getLibelle());
        dtoResponse.setCodeTypePrime(saved.getTypePrime().getCodeTypePrime());
        dtoResponse.setCoefficient(saved.getCoefficient());
        dtoResponse.setDateDebut(saved.getId().getDateDebut());
        dtoResponse.setCreatedAt(saved.getCreatedAt());
        dtoResponse.setUpdatedAt(saved.getUpdatedAt());

        return dtoResponse;
    }


    @Override
    @Transactional
    public void deleteFonction(String codeFonction) {
        rFonctionTypePrimeRepository.deleteByCodeFonctionAndCodeTypePrime(codeFonction, "TSD");
        fonctionRepository.deleteByCodeFonction(codeFonction);
    }
}