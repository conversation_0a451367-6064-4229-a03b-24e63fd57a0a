package bj.douanes.personal.model;

import java.io.IOException;
import java.time.LocalDate;
import java.util.Base64;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonManagedReference;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@Table(name = "Personnel")
public class Agent {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String email;
    private String matricule;
    private String nom;
    private String prenom;
    private String sexe;
    private LocalDate dateNais;
    private LocalDate dateMariage;
    private String situationFam;
    private String lieuNais;
    private int enfantCharge;
    private int nbrEnfant;
    private String addr1;
    private String addr2;
    private String bp;
    private String cell;
    private String observation;
    private String categorie;

    @Lob
    @Column(name = "profil", columnDefinition = "MEDIUMBLOB")
    private byte[] profil; 

    @Transient
    private MultipartFile imageFile;


    @OneToOne(mappedBy = "agent", cascade = CascadeType.ALL,orphanRemoval = true)
    @JsonManagedReference
    private SituationAd situationAd;

    @OneToOne(mappedBy = "agent", cascade = CascadeType.ALL,orphanRemoval = true)
    @JsonManagedReference
    private SituationFnc situationFnc;

    @OneToMany(mappedBy = "agent",cascade = CascadeType.ALL)
    private List<Certification> diplomes;

    @OneToMany(mappedBy = "agent",cascade = CascadeType.ALL)
    private List<Prime> prime;

    @OneToMany(mappedBy = "agent", cascade = CascadeType.ALL)
    @JsonManagedReference
    private List<Notation> notations;

    @OneToMany(mappedBy = "agent", cascade = CascadeType.ALL)
    @JsonManagedReference
    private List<Actage> actage;

    @OneToMany(mappedBy = "agent", cascade = CascadeType.ALL)
    @JsonManagedReference
    private List<AgentChildren> agentChildrens;

    @OneToMany(mappedBy = "agent", cascade = CascadeType.ALL)
    @JsonManagedReference
    private List<AgentConjoint> agentConjoints;

    @OneToMany(mappedBy = "agent", cascade = CascadeType.ALL)
    @JsonManagedReference
    private List<MutationNom> mutation;

    @OneToMany(mappedBy = "agent", cascade = CascadeType.ALL)
    @JsonManagedReference
    private List<Langues> langues;

    @OneToMany(mappedBy = "agent", cascade = CascadeType.ALL)
    @JsonManagedReference
    private List<Sanctions> santions;

    @OneToMany(mappedBy = "agent", cascade = CascadeType.ALL)
    @JsonManagedReference
    private List<DebPosition> debPosition;

    @OneToMany(mappedBy = "agent", cascade = CascadeType.ALL)
    @JsonManagedReference
    private List<FinPosition> finPosition;


    @PrePersist
    private void assignAgentToOrders() {
        if (this.agentChildrens != null) {
            this.agentChildrens.forEach(child -> child.setAgent(this));
        }
        if (this.agentConjoints != null) {
            this.agentConjoints.forEach(conjoint -> conjoint.setAgent(this));
        }
        if (this.actage != null) {
            this.actage.forEach(act -> act.setAgent(this));
        }
        if (this.notations != null) {
            this.notations.forEach(note -> note.setAgent(this));
        }
        if (this.diplomes != null) {
            this.diplomes.forEach(diplome -> diplome.setAgent(this));
        }
        if (this.prime != null) {
            this.prime.forEach(prime -> prime.setAgent(this));
        }
        if (this.mutation != null) {
            this.mutation.forEach(mutation -> mutation.setAgent(this));
        }
        if (this.langues != null) {
            this.langues.forEach(langues -> langues.setAgent(this));
        }
        if (this.santions != null) {
            this.santions.forEach(santions -> santions.setAgent(this));
        }
        if (this.debPosition != null) {
            this.debPosition.forEach(debPosition -> debPosition.setAgent(this));
        }
        if (this.finPosition != null) {
            this.finPosition.forEach(finPosition -> finPosition.setAgent(this));
        }
        if (this.situationAd != null) {
            this.situationAd.setAgent(this);
        }
        if (this.situationFnc != null) {
            this.situationFnc.setAgent(this);
        }
    } 

     // Méthode pour charger l'image depuis un MultipartFile
    public void setImageFile(MultipartFile file) throws IOException {
        if (file != null && !file.isEmpty()) {
            this.profil = file.getBytes();
        }
    }

    // Méthode pour obtenir l'extension de fichier de l'image
    public String getImageFileExtension() {
        if (this.imageFile != null && !this.imageFile.isEmpty()) {
            String originalFilename = this.imageFile.getOriginalFilename();
            if (originalFilename != null) {
                int lastIndex = originalFilename.lastIndexOf('.');
                if (lastIndex > 0) {
                    return originalFilename.substring(lastIndex + 1);
                }
            }
        }
        return null;
    }

    public String getImageBase64() {
        if (profil != null && profil.length > 0) {
            String encodedImage = Base64.getEncoder().encodeToString(profil);
            String extension = getImageFileExtension();
            return "data:image/" + extension + ";base64," + encodedImage;
        }
        return null;
    }


}
