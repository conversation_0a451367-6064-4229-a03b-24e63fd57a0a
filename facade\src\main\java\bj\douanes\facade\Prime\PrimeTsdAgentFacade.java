package bj.douanes.facade.Prime;

import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.DTO.PrimeTsdAgentDto;
import bj.douanes.Services.PrimeTsdAgentServ;
import lombok.RequiredArgsConstructor;

public interface PrimeTsdAgentFacade {
    // Méthode pour créer un PrimeTsdAgent
    List<PrimeTsdAgentDto> createPrimeTsdAgent(Long idRepartition);

}

@Service
@RequiredArgsConstructor
class InnerPrimeTsdAgentFacade implements PrimeTsdAgentFacade {

    private final PrimeTsdAgentServ primeTsdAgentServ;

    @Override
    public List<PrimeTsdAgentDto> createPrimeTsdAgent(Long idRepartition) {
        return primeTsdAgentServ.createPrimeTsdAgent(idRepartition);
    }
}