package bj.douanes.core.config.database;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
public class DatabaseBean {
    @Value("${app.datasource.type.names}")
    public List<String> envTypeNames;

    @Bean
    public DataSourceType dataSourceType() {
        return new DataSourceType(envTypeNames);
    }

}
