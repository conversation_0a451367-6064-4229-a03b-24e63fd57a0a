# Routage Automatique des DataSources

## Vue d'ensemble

Le système de routage automatique des datasources permet de déterminer automatiquement quelle base de données utiliser en fonction du package du service appelé, sans avoir besoin d'annoter explicitement chaque méthode avec `@WithDatabase`.

## Fonctionnement

### 1. Routage Automatique par Package

L'aspect `ServiceExecutionDatabaseRouterAspect` intercepte automatiquement les appels aux services dans les packages configurés et route vers la datasource appropriée.

**Packages interceptés :**
- `bj.douanes.personal.service.*`
- `bj.douanes.transport.service.*`
- `bj.douanes.prime.service.*`
- `bj.douanes.sydoniaplusplus.service.*`
- `bj.douanes.facade.service.*`
- `bj.douanes.core.service.*`

### 2. Configuration du Mapping

Le mapping package → datasource se configure dans `application.properties` :

```properties
# Configuration du mapping package -> datasource
app.datasource.mapping.default-data-source=primary
app.datasource.mapping.packages.bj.douanes.personal=primary
app.datasource.mapping.packages.bj.douanes.transport=transport_db
app.datasource.mapping.packages.bj.douanes.prime=primary
app.datasource.mapping.packages.bj.douanes.sydoniaplusplus=oracle_db
app.datasource.mapping.packages.bj.douanes.core=primary
```

### 3. Priorité des Aspects

1. **@WithDatabase (Priorité 2)** : Si une méthode est annotée avec `@WithDatabase`, cette annotation a la priorité absolue
2. **Routage automatique (Priorité 1)** : Sinon, le routage se fait automatiquement selon le package

## Exemples d'utilisation

### Service Transport (Routage automatique)

```java
@Service
public class TransportService {
    
    // Cette méthode utilisera automatiquement la datasource 'transport_db'
    // car elle se trouve dans le package bj.douanes.transport.service
    public void createTransport(Transport transport) {
        // Opérations de base de données
    }
}
```

### Service avec Override explicite

```java
@Service
public class TransportService {
    
    // Utilise la datasource 'transport_db' (routage automatique)
    public void createTransport(Transport transport) {
        // ...
    }
    
    // Force l'utilisation de la datasource 'primary' malgré le package
    @WithDatabase("primary")
    public void syncWithMainDatabase() {
        // ...
    }
}
```

## Configuration Multi-DataSources

### Exemple de configuration complète

```properties
# Définition des datasources
app.datasource.type.names=primary,transport_db,oracle_db

# Primary DB (PostgreSQL)
app.datasource.primary.url=*****************************************
app.datasource.primary.username=postgres
app.datasource.primary.password=password
app.datasource.primary.driver-class-name=org.postgresql.Driver

# Transport DB (PostgreSQL séparée)
app.datasource.transport_db.url=**********************************************
app.datasource.transport_db.username=postgres
app.datasource.transport_db.password=password
app.datasource.transport_db.driver-class-name=org.postgresql.Driver

# Oracle DB (pour sydoniaplusplus)
app.datasource.oracle_db.url=***********************************
app.datasource.oracle_db.username=hr
app.datasource.oracle_db.password=password
app.datasource.oracle_db.driver-class-name=oracle.jdbc.OracleDriver

# Mapping des packages
app.datasource.mapping.packages.bj.douanes.transport=transport_db
app.datasource.mapping.packages.bj.douanes.sydoniaplusplus=oracle_db
```

## Tests

### Endpoints de test

- `GET /api/test/database-routing/auto` : Test du routage automatique
- `GET /api/test/database-routing/explicit` : Test du routage explicite

### Logs

Les logs montrent quelle datasource est utilisée :

```
DEBUG ServiceExecutionDatabaseRouterAspect - Routage automatique: bj.douanes.transport.service.TransportService -> datasource 'transport_db'
DEBUG ServiceExecutionDatabaseRouterAspect - Service core: bj.douanes.core.service.DatabaseRoutingTestService -> datasource par défaut 'primary'
```

## Avantages

1. **Automatique** : Plus besoin d'annoter chaque méthode
2. **Flexible** : Possibilité d'override avec `@WithDatabase`
3. **Configurable** : Mapping défini dans les propriétés
4. **Maintenable** : Centralisation de la logique de routage
5. **Performant** : Résolution du mapping au runtime avec cache

## Migration

Pour migrer de l'ancien système :

1. Supprimer les annotations `@WithDatabase` des services (optionnel)
2. Configurer le mapping dans `application.properties`
3. Les deux systèmes peuvent coexister pendant la transition
