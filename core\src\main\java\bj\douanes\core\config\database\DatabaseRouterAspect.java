package bj.douanes.core.config.database;

import lombok.RequiredArgsConstructor;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Aspect
@Component
@RequiredArgsConstructor
@Order(2) // Exécuté après ServiceExecutionDatabaseRouterAspect
public class DatabaseRouterAspect {

    private final DataSourceType dataSourceType;    
    private final TransactionalHelper transactionalHelper;
    private String valueType;

    @Around("@annotation(withDatabase)")
    public Object proceed(ProceedingJoinPoint proceedingJoinPoint, WithDatabase withDatabase) throws Throwable {
        try {
            if (withDatabase.value().isEmpty()) valueType = dataSourceType.first;
            else valueType = dataSourceType.setName( withDatabase.value().toLowerCase() );

            DatabaseContextHolder.setCtx(valueType);
            return this.transactionalHelper.runWithTransaction(proceedingJoinPoint::proceed);
        } finally {
            DatabaseContextHolder.restoreCtx();
        }
    }
}
