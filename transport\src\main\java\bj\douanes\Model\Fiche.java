package bj.douanes.Model;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PostPersist;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@Table(name = "convoyage")
public class Fiche {
    
     @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String serialNumber;

    @NotNull
    private String cuoCode;

    // @NotNull
    // private String produit;

    @NotNull
    private String numCamion;

    @NotNull
    private String nomConducteur;  

    @NotNull
    private String prenomConducteur;

    @NotNull
    private String numPermisConducteur;

    @NotNull
    private String contact;

    @NotNull
    private String nameOwner;

    @NotNull
    private String firstnameOwner;

    @NotNull
    private String contactOwner;

    @NotNull
    private List<String> produitCharge;

    @NotNull
    private List<Long> nombreSac100;

    @NotNull
    private List<Long> nombreSac50;

    @NotNull
    private List<Long> poidsProduct;

    @NotNull
    private String communeProduct;

    @NotNull
    private String destinationProduct;

    @NotNull
    private String village;
    
    private String statut ="En attente";

    @NotNull
    private LocalDate date;

     public Date getDateAsDate() {
        return Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    //Generer un numero de serie aleatoire qui ne sera jamais dupliquer
    @PostPersist
    private void generateSerialNumber() {
        this.serialNumber = communeProduct + "-" + (int)(Math.random() * 10000);
    }
}
