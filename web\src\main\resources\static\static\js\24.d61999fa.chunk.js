"use strict";(self.webpackChunkfrontend_erp_douanes_benin=self.webpackChunkfrontend_erp_douanes_benin||[]).push([[24],{6024:(e,s,n)=>{n.r(s),n.d(s,{default:()=>E});var l,a,r=n(7528),i=n(5043),t=n(618),o=n(4e3),c=n(2536),d=n(6051),m=n(5444),p=n(2513),x=n(7021),h=n(7170),u=n(9456),g=n(9721),j=n(7747),A=n(513),b=n(2343),k=n(9092),f=n(6445),y=n(2518),w=n(2080),N=n(8369),C=n(1244),v=n(807),_=n(6540),I=n(579);const D={username:"",password:"",remember:!1},E=()=>{const e=q(),{theme:s,device:n,locale:l}=(0,u.d4)((e=>e.global)),a=(0,u.wA)(),{t:r}=(0,k.B)(),{setLanguage:h}=(0,_.o)(),[v,E]=(0,i.useState)(!1),{message:B}=t.A.useApp();return(0,I.jsx)("div",{className:e["login-page"],children:(0,I.jsxs)(o.A,{onFinish:async e=>{E(!0);const s=await a((0,b.Zm)(e));E(!1),s.payload.logged||B.error(s.payload.message)},className:e.form,initialValues:D,children:[(0,I.jsxs)("div",{className:"flex justify-content-between",children:[(0,I.jsx)(c.A,{menu:{onClick:e=>(e=>{let{key:s}=e;a((0,C.Yl)(s))})(e),items:["dark"===s?{key:"light",icon:(0,I.jsx)(f.A,{}),label:r("Light")}:{key:"dark",icon:(0,I.jsx)(y.A,{}),label:r("Dark")}]},children:(0,I.jsx)("a",{onClick:e=>e.preventDefault(),children:(0,I.jsxs)(d.A,{children:["dark"===s?(0,I.jsx)(y.A,{}):(0,I.jsx)(w.A,{}),(0,I.jsx)(N.A,{})]})})}),(0,I.jsx)(c.A,{menu:{onClick:e=>(e=>{let{key:s}=e;a((0,C.xS)(s)),h(s)})(e),items:[{key:"en",icon:(0,I.jsx)(j.h,{}),disabled:"en"===l,label:r("English")},{key:"fr",icon:(0,I.jsx)(A.h,{}),disabled:"fr"===l,label:r("French")}]},children:(0,I.jsx)("a",{onClick:e=>e.preventDefault(),children:(0,I.jsxs)(d.A,{children:[l,(0,I.jsx)(N.A,{})]})})})]}),(0,I.jsx)("img",{className:"block m-auto",src:g.A,alt:""}),(0,I.jsx)("h2",{className:"text-center my-2",children:r("CONNEXION")}),(0,I.jsx)(o.A.Item,{name:"username",rules:[{required:!0,message:r("global.tips.enterEmailMessage")}],children:(0,I.jsx)(m.A,{placeholder:"<EMAIL>"})}),(0,I.jsx)(o.A.Item,{name:"password",rules:[{required:!0,message:r("global.tips.enterPasswordMessage")}],children:(0,I.jsx)(m.A,{type:"password",placeholder:r("global.tips.password")})}),(0,I.jsx)(o.A.Item,{name:"remember",valuePropName:"checked",children:(0,I.jsx)(p.A,{children:r("global.tips.rememberUser")})}),(0,I.jsx)(o.A.Item,{children:(0,I.jsx)(x.Ay,{htmlType:"submit",type:"primary",className:"w-full",loading:v,children:r("global.tips.login")})}),(0,I.jsx)("p",{className:"text-center font-bold",children:r("global.tips.footer")})]})})},q=()=>{const{token:e}=h.A.useToken();return{"login-page":(0,v.AH)(l||(l=(0,r.A)(["\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      background: ",";\n    "])),e.colorBgContainer),form:(0,v.AH)(a||(a=(0,r.A)(["\n      width: 300px;\n      padding: 50px 40px 10px;\n      border-radius: 10px;\n    "])))}}}}]);
//# sourceMappingURL=24.d61999fa.chunk.js.map