package bj.douanes.sydoniaplusplus.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jdbc.repository.config.EnableJdbcRepositories;

@Configuration
@EnableJdbcRepositories(
    basePackages = "bj.douanes.sydoniaplusplus.repository"
)
public class SydoniaplusplusDataSourceConfig {
    // Cette configuration permet au module sydoniaplusplus d'utiliser Spring Data JDBC
    // pour accéder à la base de données Oracle avec le schéma spécifique OPS$ASY
}
