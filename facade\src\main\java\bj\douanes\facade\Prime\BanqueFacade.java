package bj.douanes.facade.Prime;

import java.util.List;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import bj.douanes.DTO.BanqueDTO;
import bj.douanes.Model.Banque;
import bj.douanes.Services.BanqueServ;
import lombok.RequiredArgsConstructor;

public interface BanqueFacade {
    List<BanqueDTO> getAllBanques();
    BanqueDTO getBanqueById(String codeBanque);
    BanqueDTO createBanque(BanqueDTO banqueDTO);
    BanqueDTO updateBanque(String codeBanque, BanqueDTO banqueDTO);
    void deleteBanque(String codeBanque);
}

@Service
@RequiredArgsConstructor
class BanqueFacadeImpl implements BanqueFacade {

    private final BanqueServ banqueServ;
    private final ModelMapper modelMapper;

    @Override
    public List<BanqueDTO> getAllBanques() {
        return banqueServ.getAllbanques();
    }

    @Override
    public BanqueDTO getBanqueById(String codeBanque) {
        return banqueServ.getAllbanques().stream()
                .filter(banque -> banque.getCodeBanque().equals(codeBanque))
                .findFirst()
                .orElse(null);
    }

    @Override
    public BanqueDTO createBanque(BanqueDTO banqueDTO) {
        Banque banque = modelMapper.map(banqueDTO, Banque.class);
        Banque createdBanque = banqueServ.createBanque(banque);
        return modelMapper.map(createdBanque, BanqueDTO.class);
    }

    @Override
    public BanqueDTO updateBanque(String codeBanque, BanqueDTO banqueDTO) {
        Banque banque = modelMapper.map(banqueDTO, Banque.class);
        Banque updatedBanque = banqueServ.updateBanque(codeBanque, banque);
        return modelMapper.map(updatedBanque, BanqueDTO.class);
    }

    @Override
    public void deleteBanque(String codeBanque) {
        banqueServ.deleteBanque(codeBanque);
    }
}