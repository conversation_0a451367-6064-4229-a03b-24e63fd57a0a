package bj.douanes.personal.service;
import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Agent;
import bj.douanes.personal.model.Prime;
import bj.douanes.personal.repository.AgentRepo;
import bj.douanes.personal.repository.PrimeRepo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface PrimeServ {
    
    Prime getPrimeById(Long id);
    Prime createPrime(Long id,Prime prime);
    String deletePrime(Long id);
    List<Prime> getPrimefAllList();
}

@Slf4j
@Service
@RequiredArgsConstructor
class PrimeServImpl implements PrimeServ{

    private final PrimeRepo primeRepo;
    private final AgentRepo agentRepo;

    @Override
    public Prime getPrimeById(Long id) {
        return primeRepo.findById(id).orElse(null);
    }

    @Override
    public Prime createPrime(Long id, Prime prime) {
        Agent agent = agentRepo.findById(id).orElse(null);
        prime.setAgent(agent);
        return primeRepo.save(prime);
    }

    @Override
    public String deletePrime(Long id) {
        primeRepo.deleteById(id);
        return "Prime deleted";
    }
    @Override
    public List<Prime> getPrimefAllList() {
       return primeRepo.findAll();
    }

    
    
}