package bj.douanes.Repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import bj.douanes.Model.RAgentUnite;
import bj.douanes.Model.RAgentUniteId;

@Repository
public interface RAgentUniteRepository extends JpaRepository<RAgentUnite, RAgentUniteId>{

    //Une requette pour recuperer la liste de tous les agent dans rAgentUniteRepository
    @Query("SELECT ru.id.codeUnite, ru.id.matricule FROM RAgentUnite ru GROUP BY ru.id.codeUnite, ru.id.matricule")
    List<Object[]> findAllAgent();

    // Requête pour récupérer les agents par matricule
    @Query("SELECT ru FROM RAgentUnite ru WHERE ru.id.matricule = :matricule")
    List<RAgentUnite> findAllByMatricule(String matricule);

    //Requette pour recuperer l'unite d'un agent via son matricule
    @Query("SELECT ru.id.codeUnite FROM RAgentUnite ru WHERE ru.id.matricule = :matricule")
    String findCodeUniteByMatricule(String matricule);
}
