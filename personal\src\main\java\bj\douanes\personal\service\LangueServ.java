package bj.douanes.personal.service;

import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Agent;
import bj.douanes.personal.model.Langues;
import bj.douanes.personal.repository.AgentRepo;
import bj.douanes.personal.repository.LangueRepo;
import lombok.RequiredArgsConstructor;

public interface LangueServ {
    
    Langues getLanguesById(Long id);
    Langues createLangues(Long id,Langues langue);
    String deleteLangues(Long id);
    List<Langues> getLanguesAllList();
}

@Service
@RequiredArgsConstructor
class LangueImpl implements LangueServ{

    private final LangueRepo langueRepo;
    private final AgentRepo agentRepo;

    @Override
    public Langues getLanguesById(Long id) {
        return langueRepo.findById(id).orElse(null);
    }

    @Override
    public Langues createLangues(Long id, Langues langue) {
       Agent agent = agentRepo.findById(id).orElse(null);
       langue.setAgent(agent);
       return langueRepo.save(langue);
    }

    @Override
    public String deleteLangues(Long id) {
        langueRepo.deleteById(id);
        return "Langues supprimer";
    }

    @Override
    public List<Langues> getLanguesAllList() {
        return langueRepo.findAll();
    }
    
}
