package bj.douanes.facade.Prime;

import java.util.List;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import bj.douanes.DTO.UniteDTO;
import bj.douanes.Model.Unite;
import bj.douanes.Services.UniteServ;
import lombok.RequiredArgsConstructor;

public interface UniteFacade {
    List<UniteDTO> getAllUnites();
    UniteDTO createUnite(UniteDTO uniteDTO);
    UniteDTO updateUnite(String codeUnite, UniteDTO uniteDTO);
    void deleteUnite(String codeUnite);
}

@Service
@RequiredArgsConstructor
class UniteFacadeImpl implements UniteFacade {

    private final UniteServ uniteServ;
    private final ModelMapper modelMapper;

    @Override
    public List<UniteDTO> getAllUnites() {
        return uniteServ.getAllUnites();
    }

    @Override
    public UniteDTO createUnite(UniteDTO uniteDTO) {
        Unite unite = modelMapper.map(uniteDTO, Unite.class);
        Unite createdUnite = uniteServ.createUnite(unite);
        return modelMapper.map(createdUnite, UniteDTO.class);
    }

    @Override
    public UniteDTO updateUnite(String codeUnite, UniteDTO uniteDTO) {
        Unite unite = modelMapper.map(uniteDTO, Unite.class);
        Unite updatedUnite = uniteServ.updateUnite(codeUnite, unite);
        return modelMapper.map(updatedUnite, UniteDTO.class);
    }

    @Override
    public void deleteUnite(String codeUnite) {
        uniteServ.deleteUnite(codeUnite);
    }
}