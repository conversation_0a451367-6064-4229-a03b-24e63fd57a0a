package bj.douanes.core.config.database;

import java.util.Optional;
import java.util.Stack;

public final class DatabaseContextHolder {

    private static final ThreadLocal<Stack<String>> ctx = new ThreadLocal<>();

    public static void setCtx(String string) {
        getCtx().push(string);
    }

    public static void restoreCtx() {
        final Stack<String> ctx = getCtx();
        if (!ctx.isEmpty()) {
            ctx.pop();
        }
    }

    public static Optional<String> peekDataSource() {
        final Stack<String> ctx = getCtx();

        if (ctx.isEmpty()) {
            return Optional.empty();
        }

        return Optional.of(ctx.peek());
    }

    private static Stack<String> getCtx() {
        if (ctx.get() == null) {
            ctx.set(new Stack<>());
        }

        return ctx.get();
    }
}
