{"version": 3, "file": "static/js/24.d61999fa.chunk.js", "mappings": "uZAmBA,MAAMA,EAAgB,CACpBC,SAAU,GACVC,SAAU,GACVC,UAAU,GAoIZ,EAjIkBC,KAChB,MAAMC,EAASC,KACT,MAAEC,EAAK,OAAEC,EAAM,OAAEC,IAAWC,EAAAA,EAAAA,KAAYC,GAASA,EAAMC,SACvDC,GAAWC,EAAAA,EAAAA,OACX,EAAEC,IAAMC,EAAAA,EAAAA,MACR,YAAEC,IAAgBC,EAAAA,EAAAA,MACjBC,EAAMC,IAAWC,EAAAA,EAAAA,WAAS,IAC3B,QAAEC,GAAYC,EAAAA,EAAIC,SAoBxB,OACEC,EAAAA,EAAAA,KAAA,OAAKC,UAAWrB,EAAO,cAAcsB,UACnCC,EAAAA,EAAAA,MAACC,EAAAA,EAAI,CAACC,SApBSC,UACjBX,GAAQ,GACR,MAAMY,QAAYnB,GAASoB,EAAAA,EAAAA,IAAQC,IACnCd,GAAQ,GACHY,EAAIG,QAAQC,QAAQd,EAAQe,MAAML,EAAIG,QAAQb,QAAQ,EAgB7BI,UAAWrB,EAAO6B,KAAMlC,cAAeA,EAAc2B,SAAA,EAC/EC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,+BAA8BC,SAAA,EAC3CF,EAAAA,EAAAA,KAACa,EAAAA,EAAQ,CACPC,KAAM,CACJC,QAASC,GAXCC,KAAc,IAAb,IAAEC,GAAKD,EAC5B7B,GAAS+B,EAAAA,EAAAA,IAASD,GAAK,EAUIE,CAAcJ,GAC/BK,MAAO,CACK,SAAVvC,EACE,CACEoC,IAAK,QACLI,MAAMtB,EAAAA,EAAAA,KAACuB,EAAAA,EAAS,IAChBC,MAAOlC,EAAE,UAEX,CACE4B,IAAK,OACLI,MAAMtB,EAAAA,EAAAA,KAACyB,EAAAA,EAAU,IACjBD,MAAOlC,EAAE,WAGfY,UAEFF,EAAAA,EAAAA,KAAA,KAAGe,QAAUW,GAAMA,EAAEC,iBAAiBzB,UACpCC,EAAAA,EAAAA,MAACyB,EAAAA,EAAK,CAAA1B,SAAA,CACO,SAAVpB,GAAmBkB,EAAAA,EAAAA,KAACyB,EAAAA,EAAU,KAAMzB,EAAAA,EAAAA,KAAC6B,EAAAA,EAAW,KACjD7B,EAAAA,EAAAA,KAAC8B,EAAAA,EAAY,YAInB9B,EAAAA,EAAAA,KAACa,EAAAA,EAAQ,CACPC,KAAM,CACJC,QAASC,GA1CEe,KAAc,IAAb,IAAEb,GAAKa,EAC7B3C,GAAS4C,EAAAA,EAAAA,IAAUd,IACnB1B,EAAY0B,EAAI,EAwCWe,CAAejB,GAChCK,MAAO,CACL,CACEH,IAAK,KACLI,MAAMtB,EAAAA,EAAAA,KAACkC,EAAAA,EAAO,IACdC,SAAqB,OAAXnD,EACVwC,MAAOlC,EAAE,YAEX,CACE4B,IAAK,KACLI,MAAMtB,EAAAA,EAAAA,KAACoC,EAAAA,EAAO,IACdD,SAAqB,OAAXnD,EACVwC,MAAOlC,EAAE,aAGbY,UAEFF,EAAAA,EAAAA,KAAA,KAAGe,QAAUW,GAAMA,EAAEC,iBAAiBzB,UACpCC,EAAAA,EAAAA,MAACyB,EAAAA,EAAK,CAAA1B,SAAA,CACHlB,GACDgB,EAAAA,EAAAA,KAAC8B,EAAAA,EAAY,eAKrB9B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAeoC,IAAKC,EAAAA,EAAWC,IAAI,MAClDvC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mBAAkBC,SAAEZ,EAAE,gBACpCU,EAAAA,EAAAA,KAACI,EAAAA,EAAKoC,KAAI,CACRC,KAAK,WACLC,MAAO,CACL,CACEC,UAAU,EACV9C,QAASP,EAAE,mCAEbY,UAEFF,EAAAA,EAAAA,KAAC4C,EAAAA,EAAK,CACJC,YAAY,wBAGhB7C,EAAAA,EAAAA,KAACI,EAAAA,EAAKoC,KAAI,CACRC,KAAK,WACLC,MAAO,CACL,CACEC,UAAU,EACV9C,QAASP,EAAE,sCAEbY,UAEFF,EAAAA,EAAAA,KAAC4C,EAAAA,EAAK,CACJE,KAAK,WACLD,YAAavD,EAAE,6BAGnBU,EAAAA,EAAAA,KAACI,EAAAA,EAAKoC,KAAI,CAACC,KAAK,WAAWM,cAAc,UAAS7C,UAChDF,EAAAA,EAAAA,KAACgD,EAAAA,EAAQ,CAAA9C,SACNZ,EAAE,iCAGPU,EAAAA,EAAAA,KAACI,EAAAA,EAAKoC,KAAI,CAAAtC,UACRF,EAAAA,EAAAA,KAACiD,EAAAA,GAAM,CAACC,SAAS,SAASJ,KAAK,UAAU7C,UAAU,SAASkD,QAASzD,EAAKQ,SACvEZ,EAAE,0BAIPU,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAEZ,EAAE,4BAEtC,EAMJT,EAAYA,KAChB,MAAM,MAAEuE,GAAUC,EAAAA,EAAUC,WAC5B,MAAO,CACL,cAAcC,EAAAA,EAAAA,IAAGC,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,uHAIDL,EAAMM,kBAEtBjD,MAAM8C,EAAAA,EAAAA,IAAGI,IAAAA,GAAAF,EAAAA,EAAAA,GAAA,+FAKV,C", "sources": ["app/views/pages/LoginPage.jsx"], "sourcesContent": ["import { useState } from 'react';\n\nimport { App, Button, Checkbox, Dropdown, Form, Input, Space, theme as antdTheme } from 'antd';\nimport { useDispatch, useSelector } from 'react-redux';\n\nimport DouaneSvg from 'assets/logo/douane.svg';\nimport { ReactComponent as EnUsSvg } from 'assets/header/en_US.svg';\nimport { ReactComponent as ZhCnSvg } from 'assets/header/zh_CN.svg';\n\nimport { doLogin } from 'app/stores/slices/auth.slice';\n\nimport { useTranslation } from 'react-i18next';\n\nimport { DownOutlined, MoonFilled, SunFilled, SunOutlined } from '@ant-design/icons';\nimport { setLocale, setTheme } from 'app/stores/slices/global.slice';\nimport { css, cx } from '@emotion/css';\nimport { useLanguage } from 'app/hooks';\n\n\nconst initialValues = {\n  username: '',\n  password: '',\n  remember: false\n};\n\nconst LoginForm = () => {\n  const styles = useStyles();\n  const { theme, device, locale } = useSelector(state => state.global);\n  const dispatch = useDispatch();\n  const { t } = useTranslation();\n  const { setLanguage } = useLanguage();\n  const [load, setLoad] = useState(false);\n  const { message } = App.useApp();\n\n  const onFinished = async (form) => {\n    setLoad(true);\n    const res = await dispatch(doLogin(form));\n    setLoad(false);\n    if (!res.payload.logged) message.error(res.payload.message);\n  };\n\n  const onChangeLocale = ({ key }) => {\n    dispatch(setLocale(key));\n    setLanguage(key);\n    // localStorage.setItem(API_STORAGE_LOCALE, key);\n  };\n\n  const onChangeTheme = ({ key }) => {\n    dispatch(setTheme(key));\n    // localStorage.setItem(API_STORAGE_THEME, newTheme);\n  };\n\n  return (\n    <div className={styles['login-page']}>\n      <Form onFinish={onFinished} className={styles.form} initialValues={initialValues}>\n        <div className=\"flex justify-content-between\">\n          <Dropdown\n            menu={{\n              onClick: info => onChangeTheme(info),\n              items: [\n                theme === 'dark' ?\n                  {\n                    key: 'light',\n                    icon: <SunFilled />,\n                    label: t('Light')\n                  } :\n                  {\n                    key: 'dark',\n                    icon: <MoonFilled />,\n                    label: t('Dark')\n                  }\n              ],\n            }}\n          >\n            <a onClick={(e) => e.preventDefault()}>\n              <Space>\n                {theme === 'dark' ? <MoonFilled /> : <SunOutlined />}\n                <DownOutlined />\n              </Space>\n            </a>\n          </Dropdown>\n          <Dropdown\n            menu={{\n              onClick: info => onChangeLocale(info),\n              items: [\n                {\n                  key: 'en',\n                  icon: <EnUsSvg />,\n                  disabled: locale === 'en',\n                  label: t('English'),\n                },\n                {\n                  key: 'fr',\n                  icon: <ZhCnSvg />,\n                  disabled: locale === 'fr',\n                  label: t('French'),\n                },\n              ],\n            }}\n          >\n            <a onClick={(e) => e.preventDefault()}>\n              <Space>\n                {locale}\n                <DownOutlined />\n              </Space>\n            </a>\n          </Dropdown>\n        </div>\n        <img className=\"block m-auto\" src={DouaneSvg} alt=\"\" />\n        <h2 className='text-center my-2'>{t('CONNEXION')}</h2>\n        <Form.Item\n          name=\"username\"\n          rules={[\n            {\n              required: true,\n              message: t('global.tips.enterEmailMessage'),\n            },\n          ]}\n        >\n          <Input\n            placeholder=\"<EMAIL>\"\n          />\n        </Form.Item>\n        <Form.Item\n          name=\"password\"\n          rules={[\n            {\n              required: true,\n              message: t('global.tips.enterPasswordMessage'),\n            },\n          ]}\n        >\n          <Input\n            type=\"password\"\n            placeholder={t('global.tips.password')}\n          />\n        </Form.Item>\n        <Form.Item name=\"remember\" valuePropName=\"checked\">\n          <Checkbox>\n            {t(\"global.tips.rememberUser\")}\n          </Checkbox>\n        </Form.Item>\n        <Form.Item>\n          <Button htmlType=\"submit\" type=\"primary\" className='w-full' loading={load}>\n            {t(\"global.tips.login\")}\n          </Button>\n        </Form.Item>\n        {/* <Link to=\"/session/signup\">Register</Link> */}\n        <p className='text-center font-bold'>{t(\"global.tips.footer\")}</p>\n      </Form>\n    </div>\n  );\n};\n\nexport default LoginForm;\n\nconst useStyles = () => {\n  const { token } = antdTheme.useToken();\n  return {\n    'login-page': css`\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      background: ${token.colorBgContainer};\n    `,\n    form: css`\n      width: 300px;\n      padding: 50px 40px 10px;\n      border-radius: 10px;\n    `\n  }\n}\n"], "names": ["initialValues", "username", "password", "remember", "LoginForm", "styles", "useStyles", "theme", "device", "locale", "useSelector", "state", "global", "dispatch", "useDispatch", "t", "useTranslation", "setLanguage", "useLanguage", "load", "setLoad", "useState", "message", "App", "useApp", "_jsx", "className", "children", "_jsxs", "Form", "onFinish", "async", "res", "do<PERSON><PERSON><PERSON>", "form", "payload", "logged", "error", "Dropdown", "menu", "onClick", "info", "_ref2", "key", "setTheme", "onChangeTheme", "items", "icon", "SunFilled", "label", "MoonFilled", "e", "preventDefault", "Space", "SunOutlined", "DownOutlined", "_ref", "setLocale", "onChangeLocale", "EnUsSvg", "disabled", "ZhCnSvg", "src", "DouaneSvg", "alt", "<PERSON><PERSON>", "name", "rules", "required", "Input", "placeholder", "type", "valuePropName", "Checkbox", "<PERSON><PERSON>", "htmlType", "loading", "token", "antdTheme", "useToken", "css", "_templateObject", "_taggedTemplateLiteral", "colorBgContainer", "_templateObject2"], "sourceRoot": ""}