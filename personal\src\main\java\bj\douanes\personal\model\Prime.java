package bj.douanes.personal.model;
import java.math.BigDecimal;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonInclude;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor 
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "Prime")
public class Prime {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String yearPrime;
    private String orthersCode;
    private BigDecimal montantComiteSuiv;
    private BigDecimal montantCagnoteClo;
    private BigDecimal montantCagnoteTs;
    private BigDecimal montantBonification;
    private LocalDate dateDebut;
    private LocalDate dateFin;
    private Long nbrJrs;
    private String titreEtat;
    private String codeType;
    private Boolean clopinetteTs;
    private Boolean archiver;
    private Boolean tenirCompteAncienP;
    // private BigDecimal totalPointClop;
    // private BigDecimal totalPointTs;
    // private BigDecimal totalPointPoint;
    // private BigDecimal totalPointClop;
    // private BigDecimal totalPointClop;

    @ManyToOne
    @JoinColumn(name = "agent_id")
    @JsonBackReference
    private Agent agent;
    
}
