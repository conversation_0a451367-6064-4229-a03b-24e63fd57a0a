package bj.douanes.web;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.facade.AuthFacade;
import bj.douanes.facade.UTILS.DTO;
import bj.douanes.utils.AppResponse;
import bj.douanes.utils.Endpoints;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("api/admin/user")
public class AuthCrudController implements Endpoints {

    private final AuthFacade facade;

    @GetMapping
    public ResponseEntity<?> getAllUsers() {
        return AppResponse.ok(facade.getAllUsers());
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getUserById(@PathVariable Long id) {
        return AppResponse.ok(facade.getUserById(id));
    }

    @PostMapping
    public ResponseEntity<?> createUser(@Valid @RequestBody DTO.UserReq user) {
        return AppResponse.created(facade.saveUser(user));
    }

    @PutMapping("/{id}")
    public ResponseEntity<?> updateUser(@PathVariable Long id, @RequestBody DTO.UserReq user) {
        return AppResponse.ok(facade.updateUser(id, user));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteUser(@PathVariable Long id) {
        return AppResponse.ok(facade.deleteUser(id));
    }

}