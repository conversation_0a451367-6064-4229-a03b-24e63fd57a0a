package bj.douanes.core.service;

import bj.douanes.core.config.database.WithDatabase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service de test pour démontrer le routage automatique des datasources
 */
@Slf4j
@Service
public class DatabaseRoutingTestService {
    
    /**
     * Cette méthode sera automatiquement routée vers la datasource 'primary'
     * car elle se trouve dans le package bj.douanes.core
     */
    public void testCoreServiceRouting() {
        log.info("Exécution d'une méthode du service Core - devrait utiliser la datasource 'primary'");
        // Ici vous pourriez faire des opérations de base de données
        // qui utiliseront automatiquement la datasource configurée pour le package core
    }
    
    /**
     * Méthode avec annotation explicite - l'annotation @WithDatabase a la priorité
     * sur le routage automatique
     */
    @WithDatabase("secondary")
    public void testExplicitRouting() {
        log.info("Exécution avec annotation @WithDatabase - devrait utiliser la datasource 'secondary'");
        // Cette méthode utilisera la datasource 'secondary' même si elle est dans le package core
    }
}
