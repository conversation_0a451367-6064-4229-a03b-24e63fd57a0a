package bj.douanes.facade;

import java.util.List;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Notation;
import bj.douanes.personal.service.NotationServ;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface NotationFacade {
    
    Notation getNotationById(Long id);
    Notation createNotation(Long id,Object notation);
    String deleteNotation(Long id);
    List<Notation> getNotationAllList();
    Notation update(Long id,Object notation);
}

@Slf4j
@Service
@RequiredArgsConstructor
class NotationImpl implements NotationFacade{

    private final ModelMapper modelMapper;
    private final NotationServ notationServ;

    @Override
    public Notation getNotationById(Long id) {
       return notationServ.getNotationById(id);
    }

    @Override
    public Notation createNotation(Long id, Object notation) {
        var newNote = modelMapper.map(notation, Notation.class);
        return notationServ.createNotation(id, newNote);
    }

    @Override
    public String deleteNotation(Long id) {
        return notationServ.deleteNotation(id);
    }

    @Override
    public List<Notation> getNotationAllList() {
        return notationServ.getNotationAllList();
    }

    @Override
    public Notation update(Long id, Object notation) {
        log.info("Implementation de la mise a jour");
        Notation notation2 = notationServ.getNotationById(id);
        log.info("Avant le mapping: {}", notation2);
        modelMapper.map(notation, notation2);
        return notationServ.createNotation(id, notation2);
    }
    
}