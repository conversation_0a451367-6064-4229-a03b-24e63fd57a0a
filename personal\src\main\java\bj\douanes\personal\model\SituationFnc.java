package bj.douanes.personal.model;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonInclude;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@Table(name = "SituationFnc")
public class SituationFnc {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String comSuivEscort;
    private String codeCorps;
    private String gradePaye;
    private double coefPaye;
    private String gradeReel;
    private double coefReel;
    private String fonction;
    private LocalDate dateEffet;
    private String primeForfait;
    private String numeroSecuSocial;
    private String pointPaye;
    private String rib;

    @OneToOne
    @JoinColumn(name = "agent_id")
    @JsonBackReference
    private Agent agent;
}
