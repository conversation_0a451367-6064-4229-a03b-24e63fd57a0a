package bj.douanes.Repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import bj.douanes.Model.RAgentRepartition;
import bj.douanes.Model.RAgentRepartitionId;

@Repository
public interface RAgentRepartitionRepository extends JpaRepository<RAgentRepartition, RAgentRepartitionId>{


    @Query("SELECT ru FROM RAgentRepartition ru WHERE ru.id.idRepartition = :idRepartition")
    List<RAgentRepartition> findAllByIdRepartition(Long idRepartition);
    
}
