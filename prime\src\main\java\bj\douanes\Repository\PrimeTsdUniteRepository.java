package bj.douanes.Repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import bj.douanes.DTO.PrimeTSDUniteDto;
import bj.douanes.Model.PrimeTSDUnite;

@Repository
public interface PrimeTsdUniteRepository extends JpaRepository<PrimeTSDUnite, Long> {

    @Query("""
        SELECT new bj.douanes.DTO.PrimeTSDUniteDto(
            p.idPrimeUnite,
            p.montantVerset,
            p.oeuvreSocialUnite,
            p.bonificationUnite,
            p.partUnite,
            p.cumulCoef,
            p.createdAt,
            p.updatedAt,
            p.rUniteRepartition.id.codeUnite,
            p.rUniteRepartition.id.idRepartition
        )
        FROM PrimeTSDUnite p
        WHERE p.rUniteRepartition.id.idRepartition = :idRepartition
    """)
    List<PrimeTSDUniteDto> findByIdRepartition(@Param("idRepartition") Long idRepartition);


    @Query("""
        SELECT new bj.douanes.DTO.PrimeTSDUniteDto(
            p.idPrimeUnite,
            p.montantVerset,
            p.oeuvreSocialUnite,
            p.bonificationUnite,
            p.partUnite,
            p.cumulCoef,
            p.createdAt,
            p.updatedAt,
            p.rUniteRepartition.id.codeUnite,
            p.rUniteRepartition.id.idRepartition
        )
        FROM PrimeTSDUnite p
        WHERE p.rUniteRepartition.id.idRepartition = :idRepartition
        AND p.rUniteRepartition.id.codeUnite = :codeUnite
    """)
    PrimeTSDUniteDto findByIdRepartitionAndCodeUnite(@Param("idRepartition") Long idRepartition, @Param("codeUnite") String codeUnite);




}


