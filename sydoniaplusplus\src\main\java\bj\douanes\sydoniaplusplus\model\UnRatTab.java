package bj.douanes.sydoniaplusplus.model;

import java.math.BigDecimal;
import java.time.LocalDate;

// import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.*;

import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "OPS$ASY", name = "UNRATTAB")
public class UnRatTab {

    @Column("CUR_COD")
    private String curCod;

    @Column("EEA_DOV")
    private LocalDate eeaDov;

    @Column("EEA_EOV")
    private LocalDate eeaEov;

    @Column("RAT_EXC")
    private BigDecimal ratExc;

    @Column("LST_OPE")
    private String lstOpe;
    
}
