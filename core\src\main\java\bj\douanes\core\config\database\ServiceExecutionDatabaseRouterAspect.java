package bj.douanes.core.config.database;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * Aspect pour router automatiquement vers la bonne datasource
 * en fonction du package du service appelé
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
@Order(1) // Exécuté avant les autres aspects
public class ServiceExecutionDatabaseRouterAspect {

    private final DataSourceType dataSourceType;
    private final TransactionalHelper transactionalHelper;
    private final PackageDataSourceMapping packageMapping;

    /**
     * Intercepte toutes les méthodes des services dans les packages spécifiés
     * et route automatiquement vers la bonne datasource
     */
    @Around("execution(* bj.douanes.personal.service..*.*(..)) || " +
            "execution(* bj.douanes.transport.service..*.*(..)) || " +
            "execution(* bj.douanes.prime.service..*.*(..)) || " +
            "execution(* bj.douanes.sydoniaplusplus.service..*.*(..)) || " +
            "execution(* bj.douanes.facade.service..*.*(..))")
    public Object routeDataSource(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        
        // Récupérer le nom de la classe du service
        String className = proceedingJoinPoint.getTarget().getClass().getName();
        
        // Déterminer la datasource à utiliser
        String targetDataSource = packageMapping.getDataSourceForClass(className);
        
        // Valider que la datasource existe
        String validatedDataSource = dataSourceType.setName(targetDataSource.toLowerCase());
        if (validatedDataSource == null) {
            log.warn("Datasource '{}' non trouvée pour la classe '{}', utilisation de la datasource par défaut", 
                     targetDataSource, className);
            validatedDataSource = dataSourceType.first;
        }
        
        log.debug("Routage automatique: {} -> datasource '{}'", className, validatedDataSource);
        
        try {
            // Définir le contexte de la datasource
            DatabaseContextHolder.setCtx(validatedDataSource);
            
            // Exécuter la méthode dans une transaction
            return this.transactionalHelper.runWithTransaction(proceedingJoinPoint::proceed);
            
        } finally {
            // Nettoyer le contexte
            DatabaseContextHolder.restoreCtx();
        }
    }
    
    /**
     * Aspect pour les services du module core (utilise la datasource par défaut)
     */
    @Around("execution(* bj.douanes.core.service..*.*(..))")
    public Object routeCoreServices(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        String className = proceedingJoinPoint.getTarget().getClass().getName();
        log.debug("Service core: {} -> datasource par défaut '{}'", className, dataSourceType.first);
        
        try {
            DatabaseContextHolder.setCtx(dataSourceType.first);
            return this.transactionalHelper.runWithTransaction(proceedingJoinPoint::proceed);
        } finally {
            DatabaseContextHolder.restoreCtx();
        }
    }
}
