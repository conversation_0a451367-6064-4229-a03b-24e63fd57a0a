package bj.douanes.facade;
import java.util.List;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import bj.douanes.personal.model.DebPosition;
import bj.douanes.personal.service.DebPositionServ;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface DebPositionFacade {

    DebPosition getdebPositionById(Long id);
    DebPosition createdebPosition(Long agentId,Object debPosition);
    String deletedebPosition(Long id);
    List<DebPosition> getdebPositionAllList();
    DebPosition update(Long id,Object debPosition);    
}

@Slf4j
@Service
@RequiredArgsConstructor
class DebPositionImplement implements DebPositionFacade{

    private final ModelMapper modelMapper;
    private final DebPositionServ debPositionServ;

    @Override
    public DebPosition getdebPositionById(Long id) {
        return debPositionServ.getDebPositionById(id);
    }

    @Override
    public DebPosition createdebPosition(Long agentId, Object debPosition) {
        var newDebPosition = modelMapper.map(debPosition, DebPosition.class);
        return debPositionServ.createDebPosition(agentId, newDebPosition);
    }

    @Override
    public String deletedebPosition(Long id) {
        return debPositionServ.deleteDebPosition(id);
    }

    @Override
    public List<DebPosition> getdebPositionAllList() {
       return debPositionServ.getDebPositionAllList();
    }

    @Override
    public DebPosition update(Long id, Object debPosition) {
        log.info("Implementation de la mise a jour");
        DebPosition debPosition2 = debPositionServ.getDebPositionById(id);
        modelMapper.map(debPosition, debPosition2);
        return debPositionServ.createDebPosition(id, debPosition2);
    }
    
}