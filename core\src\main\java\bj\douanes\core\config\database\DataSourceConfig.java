package bj.douanes.core.config.database;

import java.util.HashMap;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jdbc.repository.config.EnableJdbcRepositories;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.JdbcTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
@EnableJdbcRepositories(basePackages = "bj.douanes", transactionManagerRef = "transactionManager", jdbcOperationsRef = "jdbcOperations")
public class DataSourceConfig {

    private final DataSourceType dataSourceType;
    private final DatabaseComponent dbComponent;

    private boolean isAlreadyBuild = false;
    private Map<Object, Object> dataSourcesBuild;

    @Bean
    @Primary
    public DataSource getDataSource() {
        if (!isAlreadyBuild) {
            isAlreadyBuild = !isAlreadyBuild;
            dataSourcesBuild = this.buildDataSources();
        }

        final Map<Object, Object> dataSources = dataSourcesBuild;
        final RoutingDataSource routingDataSource = new RoutingDataSource();
        routingDataSource.setTargetDataSources(dataSources);
        routingDataSource.setDefaultTargetDataSource(dataSources.get(dataSourceType.first));
        return routingDataSource;
    }

    @Bean
    @Primary
    public JdbcTemplate jdbcTemplate() {
        return new JdbcTemplate(getDataSource());
    }

    @Bean
    @Primary
    public PlatformTransactionManager transactionManager() {
        return new JdbcTransactionManager(getDataSource());
    }

    @Bean
    @Primary
    public NamedParameterJdbcOperations jdbcOperations() {
        return new NamedParameterJdbcTemplate(getDataSource());
    }

    private Map<Object, Object> buildDataSources() {
        final Map<Object, Object> result = new HashMap<>();

        for (String sourceType : dataSourceType.getEnvs()) {
            result.put(sourceType, this.buildDataSource(sourceType));
        }

        return result;
    }

    private DataSource buildDataSource(String sourceType) {
        final HikariConfig config = new HikariConfig();

        config.setJdbcUrl(this.dbComponent.getEnv(sourceType, "url"));
        config.setUsername(this.dbComponent.getEnv(sourceType, "username"));
        config.setPassword(this.dbComponent.getEnv(sourceType, "password"));
        config.setDriverClassName(this.dbComponent.getEnv(sourceType, "driver-class-name"));

        config.setAutoCommit(false);

        // config.setMaximumPoolSize(15);

        return new HikariDataSource(config);
    }
}