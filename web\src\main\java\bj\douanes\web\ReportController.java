package bj.douanes.web;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.personal.service.JasperReportService;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("api/agents/reports")
public class ReportController {
    
    @Autowired
    private JasperReportService jasperReportService;

    @GetMapping("/agents/download")
    public ResponseEntity<byte[]> downloadAgentReport(
            @RequestParam(required = false, defaultValue = "pdf") String format) throws Exception {
        // Paramètres pour le rapport
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("ReportTitle", "Liste des Agents");

        // Générer le rapport dans le format demandé
        byte[] reportContent = jasperReportService.generateAgentReport(parameters, format);

        // Configurer le type de fichier et le nom en fonction du format
        String fileExtension = format.toLowerCase();
        MediaType mediaType;

        switch (fileExtension) {
            case "pdf":
                mediaType = MediaType.APPLICATION_PDF;
                break;
            case "csv":
                mediaType = MediaType.TEXT_PLAIN;
                break;
            case "excel":
                mediaType = MediaType.APPLICATION_OCTET_STREAM;
                break;
            default:
                throw new IllegalArgumentException("Format non pris en charge : " + format);
        }

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=agent_report." + fileExtension)
                .contentType(mediaType)
                .body(reportContent);
    }
}
