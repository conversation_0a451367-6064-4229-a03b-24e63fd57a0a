{"version": 3, "file": "static/js/863.5fcbfe8d.chunk.js", "mappings": "qRAMA,MAgDA,EAhDmBA,KAGfC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,GAAEC,UACfC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,+BAA8BC,SAAA,EAC3CF,EAAAA,EAAAA,KAACI,EAAAA,EAAWC,MAAK,CAAAH,SAAC,mBAClBF,EAAAA,EAAAA,KAACM,EAAAA,GAAM,CAACC,KAAK,UAASL,SAAC,iBAEzBF,EAAAA,EAAAA,KAACQ,EAAAA,EAAI,CAACP,UAAU,cAAcQ,iBAAiB,IAAIC,MAAO,CACxD,CACEC,IAAK,IACLC,MAAO,QACPV,UAAWC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EAC9BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQC,SAAA,EACrBF,EAAAA,EAAAA,KAACa,EAAAA,EAAK,CACJC,MAAO,IACPC,IAAI,kFAENf,EAAAA,EAAAA,KAACM,EAAAA,GAAM,CAAAJ,SAAC,qBAEVC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,QAAOC,SAAA,EACpBF,EAAAA,EAAAA,KAACgB,EAAAA,EAAK,CAACC,YAAY,iBACnBjB,EAAAA,EAAAA,KAACgB,EAAAA,EAAK,CAACC,YAAY,iBACnBjB,EAAAA,EAAAA,KAACgB,EAAAA,EAAK,CAACC,YAAY,iBACnBjB,EAAAA,EAAAA,KAACgB,EAAAA,EAAK,CAACC,YAAY,iBACnBjB,EAAAA,EAAAA,KAACgB,EAAAA,EAAK,CAACC,YAAY,iBACnBjB,EAAAA,EAAAA,KAACgB,EAAAA,EAAK,CAACC,YAAY,iBACnBjB,EAAAA,EAAAA,KAACgB,EAAAA,EAAK,CAACC,YAAY,uBAIzB,CACEN,IAAK,IACLC,MAAO,QACPV,SAAU,yBAEZ,CACES,IAAK,IACLC,MAAO,QACPV,SAAU,kCAUPgB,EAAAA,EAAAA,IAAGC,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,0LAMWC,EAAAA,G", "sources": ["app/views/pages/ConfigPage.jsx"], "sourcesContent": ["import { css } from '@emotion/css';\nimport { BASENAME_URI } from 'app/configs';\nimport { ReactComponent as DouaneSvg } from 'assets/logo/douane.svg';\nimport { updateAppTitle } from 'app/services';\nimport { Button, Image, Input, Tabs, Typography } from 'antd';\n\nconst ConfigPage = () => {\n  // updateAppTitle();\n  return (\n    <div className=\"\" >\n      <div>\n        <div className=\"flex justify-content-between\">\n          <Typography.Title>Configuration</Typography.Title>\n          <Button type='primary'>Modifier</Button>\n        </div>\n        <Tabs className='flex m-auto' defaultActiveKey=\"1\" items={[\n          {\n            key: '1',\n            label: 'Tab 1',\n            children: (<div className='flex'>\n              <div className=\"images\">\n                <Image\n                  width={200}\n                  src=\"https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png\"\n                />\n                <Button>Change Image</Button>\n              </div>\n              <div className=\"infos\">\n                <Input placeholder=\"Basic usage\" />\n                <Input placeholder=\"Basic usage\" />\n                <Input placeholder=\"Basic usage\" />\n                <Input placeholder=\"Basic usage\" />\n                <Input placeholder=\"Basic usage\" />\n                <Input placeholder=\"Basic usage\" />\n                <Input placeholder=\"Basic usage\" />\n              </div>\n            </div>),\n          },\n          {\n            key: '2',\n            label: 'Tab 2',\n            children: 'Content of Tab Pane 2',\n          },\n          {\n            key: '3',\n            label: 'Tab 3',\n            children: 'Content of Tab Pane 3',\n          },\n        ]} />\n      </div>\n    </div>\n  );\n};\n\nexport default ConfigPage;\n\nconst styles = css`\n  height: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background-size: cover;\n  /* background-image: url(${BASENAME_URI}/assets/homeBackground.png); */\n`;\n"], "names": ["ConfigPage", "_jsx", "className", "children", "_jsxs", "Typography", "Title", "<PERSON><PERSON>", "type", "Tabs", "defaultActiveKey", "items", "key", "label", "Image", "width", "src", "Input", "placeholder", "css", "_templateObject", "_taggedTemplateLiteral", "BASENAME_URI"], "sourceRoot": ""}