package bj.douanes.Model;

import java.io.Serializable;
import java.util.Objects;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Embeddable
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RUniteRepartitionId implements Serializable {

    @Column(name = "code_unite")
    private String codeUnite;

    @Column(name = "id_repartition")
    private Long idRepartition;

     @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof RUniteRepartitionId)) return false;
        RUniteRepartitionId that = (RUniteRepartitionId) o;
        return Objects.equals(codeUnite, that.codeUnite) &&
               Objects.equals(idRepartition, that.idRepartition);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codeUnite, idRepartition);
    }
}

