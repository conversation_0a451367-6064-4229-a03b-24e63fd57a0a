package bj.douanes.Model;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "primetsd")
public class PrimeTSD {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id_prime")
    private Long idPrime;

    @Column(name = "oeuvre_social_global")
    private BigDecimal oeuvreSocialGlobal = BigDecimal.ZERO;;

    @Column(name = "part_global")
    private BigDecimal partGlobal = BigDecimal.ZERO;;

    @Column(name = "cumul_coef_global")
    private BigDecimal cumulCoefGlobal = BigDecimal.ZERO;;

    @Column(name = "created_at")
    private LocalDate createdAt;

    @Column(name = "updated_at")
    private LocalDate updatedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "id_repartition")
    @JsonIgnore
    private Repartition repartition;
}
