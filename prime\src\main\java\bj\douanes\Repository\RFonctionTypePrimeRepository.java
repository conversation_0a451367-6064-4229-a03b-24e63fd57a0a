package bj.douanes.Repository;

import java.math.BigDecimal;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import bj.douanes.Model.RFonctionTypePrime;
import bj.douanes.Model.RFonctionTypePrimeId;

@Repository
public interface RFonctionTypePrimeRepository extends JpaRepository<RFonctionTypePrime, RFonctionTypePrimeId>{

    @Query("""
        SELECT COALESCE(SUM(rftp.coefficient), 0)
        FROM RAgentUnite rau
        JOIN RAgentFonction raf ON rau.id.matricule = raf.id.matricule
        AND (raf.dateFin IS NULL OR raf.dateFin >= CURRENT_DATE)
        AND (rau.dateFin IS NULL OR rau.dateFin >= CURRENT_DATE)
        JOIN RFonctionTypePrime rftp ON raf.id.codeFonction = rftp.id.codeFonction
        WHERE rau.id.codeUnite = :codeUnite
        AND rftp.id.codeTypePrime = 'TSD'""")
    BigDecimal getCumulCoefParUniteEtTypePrime(@Param("codeUnite") String codeUnite);

    @Query("""
        SELECT COALESCE(rftp.coefficient, 0)
        FROM RAgentFonction raf
        JOIN AgentTSD a ON a.matricule = raf.id.matricule
        JOIN RFonctionTypePrime rftp ON raf.id.codeFonction = rftp.id.codeFonction
            AND (rftp.dateFin IS NULL OR rftp.dateFin >= CURRENT_DATE)
        WHERE raf.id.matricule = :matricule
        AND a.actif = true
        AND rftp.id.codeTypePrime = 'TSD'
        AND (raf.dateFin IS NULL OR raf.dateFin >= CURRENT_DATE)
    """)
    BigDecimal getCoefficientByMatriculeAndTypePrime(@Param("matricule") String matricule);



    //Obtenir le cumul des coefficients de tout les agents pour un type de prime
    @Query("""
        SELECT COALESCE(SUM(rftp.coefficient), 0)
        FROM RAgentUnite rau
        JOIN AgentTSD a ON a.matricule = rau.id.matricule
        JOIN RAgentFonction raf ON raf.id.matricule = a.matricule
            AND (raf.dateFin IS NULL OR raf.dateFin >= CURRENT_DATE)
        JOIN RFonctionTypePrime rftp ON raf.id.codeFonction = rftp.id.codeFonction
            AND (rftp.dateFin IS NULL OR rftp.dateFin >= CURRENT_DATE)
        WHERE a.actif = true
        AND (rau.dateFin IS NULL OR rau.dateFin >= CURRENT_DATE)
        AND rau.id.codeUnite = :codeUnite
        AND rftp.id.codeTypePrime = 'TSD'
    """)
    BigDecimal getCumulCoefParUniteTSDDesAgentsActifs(@Param("codeUnite") String codeUnite);

    @Query("""
        SELECT COALESCE(SUM(rftp.coefficient), 0)
        FROM RAgentUnite rau
        JOIN AgentTSD a ON a.matricule = rau.id.matricule
        JOIN RAgentFonction raf ON raf.id.matricule = a.matricule
            AND (raf.dateFin IS NULL OR raf.dateFin >= CURRENT_DATE)
        JOIN RFonctionTypePrime rftp ON raf.id.codeFonction = rftp.id.codeFonction
            AND (rftp.dateFin IS NULL OR rftp.dateFin >= CURRENT_DATE)
        WHERE a.actif = true
        AND (rau.dateFin IS NULL OR rau.dateFin >= CURRENT_DATE)
        AND rftp.id.codeTypePrime = 'TSD'
    """)
    BigDecimal getCumulCoefTSDDesAgentsActifs();


    //find by codefonction and codeTypePrime
    @Query("""
        SELECT rftp
        FROM RFonctionTypePrime rftp
        WHERE rftp.id.codeFonction = :codeFonction
        AND rftp.id.codeTypePrime = :codeTypePrime
        AND (rftp.dateFin IS NULL OR rftp.dateFin >= CURRENT_DATE)
    """)
    RFonctionTypePrime findByCodeFonctionAndCodeTypePrime(@Param("codeFonction") String codeFonction, @Param("codeTypePrime") String codeTypePrime);
    

    //supprimer une fonction par son code
    @Modifying
    @Query("DELETE FROM RFonctionTypePrime rftp WHERE rftp.id.codeFonction = :codeFonction AND rftp.id.codeTypePrime = :codeTypePrime")
    void deleteByCodeFonctionAndCodeTypePrime(@Param("codeFonction") String codeFonction, @Param("codeTypePrime") String codeTypePrime);

    // @Query("""
    //     SELECT p.bonificationUnite
    //     FROM RAgentUnite rau
    //     JOIN Agent a ON a.matricule = rau.id.matricule
    //     JOIN PrimeTSDUnite p ON p.codeUnite = rau.id.codeUnite
    //     WHERE rau.id.matricule = :matricule
    //     AND (rau.dateFin IS NULL OR rau.dateFin >= CURRENT_DATE)
    //     AND a.actif = true
    //     AND p.idRepartition = :idRepartition
    // """)
    // BigDecimal getBonificationUniteByMatriculeAndRepartition(@Param("matricule") String matricule,@Param("idRepartition") Long idRepartition);

}
