{"version": 3, "file": "static/js/155.8ec7695e.chunk.js", "mappings": "0NAKA,MAUA,EAVmBA,KACjB,MAAMC,GAAWC,EAAAA,EAAAA,MAMjB,OAJAC,EAAAA,EAAAA,YAAU,KACR,iBAAmBF,GAASG,EAAAA,EAAAA,MAAW,EAAvC,EAA0C,GACzC,CAACH,KAEGI,EAAAA,EAAAA,KAACC,EAAAA,GAAQ,CAACC,GAAG,QAAQC,SAAO,GAAG,C", "sources": ["app/views/pages/LogoutPage.jsx"], "sourcesContent": ["import { doLogout } from 'app/stores/slices/auth.slice';\nimport { useEffect } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { Navigate } from 'react-router-dom';\n\nconst LogoutPage = () => {\n  const dispatch = useDispatch();\n\n  useEffect(() => {\n    (async () => await dispatch(doLogout()))();\n  }, [dispatch]);\n\n  return <Navigate to=\"/auth\" replace />;\n};\n\nexport default LogoutPage;\n"], "names": ["LogoutPage", "dispatch", "useDispatch", "useEffect", "doLogout", "_jsx", "Navigate", "to", "replace"], "sourceRoot": ""}