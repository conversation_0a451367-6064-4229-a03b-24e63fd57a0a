package bj.douanes.core.shared.error;

import lombok.Getter;
import org.springframework.http.HttpStatus;

@Getter
public class ApiException extends RuntimeException {

    private final HttpStatus status;
    private Throwable throwable;

    public ApiException() {
        this("");
    }

    public ApiException(String message) {
        this(message, HttpStatus.BAD_REQUEST);
    }

    public ApiException(HttpStatus status) {
        this("", status);
    }

    public ApiException(String message, HttpStatus status) {
        super(message);
        this.status = status;
    }

    public ApiException(Exception ex) {
        this(ex.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        this.throwable = ex.fillInStackTrace();
    }

    public ApiException(Exception ex, String message) {
        this(message, HttpStatus.INTERNAL_SERVER_ERROR);
        this.throwable = ex.fillInStackTrace();
    }
    
    public ApiException(String message, Exception ex) {
        this(ex,message);
    }

    public ApiException(Exception ex, String message, HttpStatus status) {
        this(message, status);
        this.throwable = ex.fillInStackTrace();
    }

    public ApiException(Exception ex, HttpStatus status) {
        this(ex.getMessage(), status);
        this.throwable = ex.fillInStackTrace();
    }

    public ApiException(Exception ex, HttpStatus status, String message) {
        this(message, status);
        this.throwable = ex.fillInStackTrace();
    }
}
