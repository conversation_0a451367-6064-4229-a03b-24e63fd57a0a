package bj.douanes.web;

import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;

@RestController
@RequestMapping("api/test")
@SecurityRequirement(name = "lockApi")
public class TestController {

    @GetMapping("/user")
    @PreAuthorize("hasAuthority('USER')")
    public ResponseEntity<String> test1(){
        return ResponseEntity.ok("HELLO USER");
    }

    @GetMapping("/admin")
    @PreAuthorize("hasAuthority('ADMIN')")
    public ResponseEntity<String> test2(){
        return ResponseEntity.ok("HELLO ADMIN");
    }
}
