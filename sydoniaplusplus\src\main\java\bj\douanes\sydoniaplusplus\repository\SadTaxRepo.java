package bj.douanes.sydoniaplusplus.repository;

import java.util.List;

import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.ListCrudRepository;
import org.springframework.stereotype.Repository;

import bj.douanes.sydoniaplusplus.model.SadTax;
import bj.douanes.sydoniaplusplus.model.SadAllKeys;

@Repository
public interface SadTaxRepo extends ListCrudRepository<SadTax, SadAllKeys> {
    
    @Query("SELECT * FROM \"OPS$ASY\".SAD_TAX WHERE ROWNUM <= 10")
    List<SadTax> findFirst10();

    @Query("SELECT * FROM \"OPS$ASY\".SAD_TAX WHERE key_year = :#{#keys.keyYear} AND key_cuo = :#{#keys.keyCuo} AND key_dec = :#{#keys.keyDec} AND key_nber = :#{#keys.keyNber} AND sad_num = :#{#keys.sadNum} AND itm_nber = :#{#keys.itmNber}")
    List<SadTax> findAllByKeys(Sad<PERSON>llK<PERSON><PERSON> keys);
}
