package bj.douanes.core.auth.repository;

import java.util.Optional;

import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.ListCrudRepository;

import bj.douanes.core.auth.model.User;

public interface UserRepo extends ListCrudRepository<User, Long> {
    @Query("SELECT * FROM utilisateurs WHERE email=:email")
    public Optional<User> findByEmail(String email);
}
