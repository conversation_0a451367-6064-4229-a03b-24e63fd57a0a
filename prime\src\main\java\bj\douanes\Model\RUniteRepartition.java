package bj.douanes.Model;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonInclude;

import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "r_unite_repartition")
public class RUniteRepartition {
    
    @EmbeddedId
    private RUniteRepartitionId id;

    @ManyToOne
    @MapsId("codeUnite")
    @JoinColumn(name = "code_unite")
    private Unite unite;

    @ManyToOne
    @MapsId("idRepartition")
    @JoinColumn(name = "id_repartition")
    private Repartition repartition;

    @Column(name = "id_prime_unite")
    private Long idPrimeUnite;

    @Column(name = "created_at")
    private LocalDate createdAt;

    @Column(name = "updated_at")
    private LocalDate updatedAt;

    // @OneToMany(mappedBy = "rUniteRepartition")
    // @JsonIgnore
    // private List<PrimeTSDUnite> primes;
}
