package bj.douanes.sydoniaplusplus.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "OPS$ASY", name = "UNMOPTAB")
public class UnMopTab {
    
    @Column("MOP_COD") @Id
    private String mopCod;

    @Column("MOP_DSC")
    private String mopDsc;
}
