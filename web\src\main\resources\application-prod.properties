server.port=80

app.datasource.type.names=primary

#Primary DB 
#postgres database configuration
app.datasource.primary.url=***************************************
app.datasource.primary.username=root
app.datasource.primary.password=rootoor
app.datasource.primary.driver-class-name=org.postgresql.Driver


app.jwt.secret-key=9CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED122C8898E
app.jwt.delay=24

app.security.white-list=/,/*.*,/static/**,/assets/**,/eservice/**,/h2-console/**,/v3/api-docs/**,/swagger-ui/**,\
                        /api/auth/**,/api/asy/**,\
                        /api/agents/**,/api/admin/**,/api/transport/**,/api/prime/**