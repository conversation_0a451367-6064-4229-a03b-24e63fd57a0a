package bj.douanes.Service;

import java.io.ByteArrayInputStream;

import org.springframework.stereotype.Service;

import com.itextpdf.io.font.constants.StandardFonts;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.io.source.ByteArrayOutputStream;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.properties.TextAlignment;

import bj.douanes.Model.Fiche;

@Service
public class PdfService {
    
    public ByteArrayInputStream generatePdf(Fiche fiche) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
    
        try {
            PdfWriter writer = new PdfWriter(out);
            PdfDocument pdfDoc = new PdfDocument(writer);
            Document document = new Document(pdfDoc);
            PdfFont font = PdfFontFactory.createFont(StandardFonts.COURIER_BOLD);
            PdfFont font2 = PdfFontFactory.createFont(StandardFonts.COURIER);
    
           // En-tête
            float logoWidth = 70; // Largeur de l'image
            float logoHeight = 65; // Hauteur de l'image
            // Logos
            Image leftLogo = new Image(ImageDataFactory.create("https://www.impots.finances.gouv.bj/wp-content/uploads/2016/11/logo_mef.jpg"))
                .setWidth(150)
                .setHeight(logoHeight)
                // .setMarginBottom(120)
                .setFixedPosition(50, 740);
            Image rightLogo = new Image(ImageDataFactory.create("/home/<USER>/giiiit/erp-backend/transport/src/main/resources/static/images/adresse_gdg.png"))
                .setWidth(240)
                .setHeight(logoHeight)
                .setMarginTop(30)
                .setFixedPosition(300, 740);

            // Créer un conteneur pour l'en-tête
            Paragraph header = new Paragraph()
                    .add(leftLogo)
                    // .add(new Paragraph("DIRECTION GENERALE DES DOUANES")
                    //         .setFontSize(12)
                    //         .setMarginTop(10)
                    //         .setFont(font)
                    //         .setBold()
                    //         .setTextAlignment(TextAlignment.CENTER))
                    //         .setMarginLeft(85)
                    .add(rightLogo);
            document.add(header);


            document.add(new Paragraph("Direction Générale des Douanes").setFontSize(12).setMarginTop(60).setFont(font).setBold().setTextAlignment(TextAlignment.CENTER));
            document.add(new Paragraph("FICHE DE RENSEIGNEMENT ET DE CONVOYAGE DES PRODUITS AGRICOLES").setFontSize(12).setFont(font).setBold().setTextAlignment(TextAlignment.CENTER));
        
            // Détails de ma fiche
            Paragraph serviceDouanes = new Paragraph()
                .add(new Paragraph("Service des douanes: ").setFontSize(12).setFont(font2).setUnderline().setBold())
                .add(new Paragraph(fiche.getCuoCode()).setFontSize(12).setFont(font2));
                document.add(serviceDouanes.setMarginBottom(8).setMarginLeft(12));
            Paragraph produit = new Paragraph()
                .add(new Paragraph("Produit chargé: ").setFontSize(12).setFont(font2).setUnderline().setBold());
                //.add(new Paragraph(fiche.getProduit()).setFontSize(12).setFont(font2));
                document.add(produit.setMarginBottom(8).setMarginLeft(12));
            Paragraph camion = new Paragraph()
                .add(new Paragraph("N° Camion: ").setFontSize(12).setFont(font2).setUnderline().setBold())
                .add(new Paragraph(fiche.getNumCamion()).setFontSize(12).setFont(font2));
                document.add(camion.setMarginBottom(8).setMarginLeft(12));
            Paragraph nomprenomchauffeur = new Paragraph()
                .add(new Paragraph("Nom et Prénoms du Chauffeur: ").setFontSize(12).setFont(font2).setUnderline().setBold())
                .add(new Paragraph(fiche.getNomConducteur() +" "+ fiche.getPrenomConducteur() ).setFontSize(12).setFont(font2));
                document.add(nomprenomchauffeur.setMarginBottom(8).setMarginLeft(12));
               
            Paragraph contacts = new Paragraph()
                .add(new Paragraph("Contacts: ").setFontSize(12).setFont(font2).setUnderline().setBold())
                    .add(new Paragraph(fiche.getContact()).setFontSize(12).setFont(font2));
                document.add(contacts.setMarginBottom(8).setMarginLeft(12));
                Paragraph permisConduire = new Paragraph()
                    .add(new Paragraph("Permis de Conduire: ").setFontSize(12).setFont(font2).setUnderline().setBold())
                    .add(new Paragraph(fiche.getNumPermisConducteur()).setFontSize(12).setFont(font2));
                document.add(permisConduire.setMarginBottom(8).setMarginLeft(12));
                Paragraph nomPrenomOwner = new Paragraph()
                    .add(new Paragraph("Nom et Prénoms du Propriétaire du produit: ").setFontSize(12).setFont(font2).setUnderline().setBold())
                    .add(new Paragraph(fiche.getNameOwner() +" "+ fiche.getFirstnameOwner()).setFontSize(12).setFont(font2));
                document.add(nomPrenomOwner.setMarginBottom(8).setMarginLeft(12));
                Paragraph contactsOwner = new Paragraph()
                    .add(new Paragraph("Contact: ").setFontSize(12).setFont(font2).setUnderline().setBold())
                    .add(new Paragraph(fiche.getContactOwner()).setFontSize(12).setFont(font2));
                document.add(contactsOwner.setMarginBottom(8).setMarginLeft(12));
                Paragraph numberSacPoids = new Paragraph()
                    .add(new Paragraph("Nombre de Sacs et poids: ").setFontSize(12).setFont(font2).setUnderline().setBold())
                    .add(new Paragraph(fiche.getNombreSac100()+" sac de 100  /" + fiche.getNombreSac50()+" sac de 50  /" + fiche.getPoidsProduct()+" kg").setFontSize(12).setFont(font2));
                document.add(numberSacPoids.setMarginBottom(8).setMarginLeft(12));
                Paragraph venant = new Paragraph()
                    .add(new Paragraph("Venant de: ").setFontSize(12).setFont(font2).setUnderline().setBold())
                    .add(new Paragraph(fiche.getCommuneProduct()).setFontSize(12).setFont(font2));
                document.add(venant.setMarginBottom(8).setMarginLeft(12));
                Paragraph allant = new Paragraph()
                    .add(new Paragraph("Allant à: ").setFontSize(12).setFont(font2).setUnderline().setBold())
                    .add(new Paragraph(fiche.getDestinationProduct()).setFontSize(12).setFont(font2));
                document.add(allant.setMarginBottom(8).setMarginLeft(12));
                Paragraph fait = new Paragraph()
                        .add(new Paragraph("Fait le: ").setFontSize(12).setFont(font2).setUnderline().setBold())
                        .add(new Paragraph(new java.text.SimpleDateFormat("dd/MM/yyyy").format(fiche.getDateAsDate())).setFontSize(12).setFont(font2));
                    document.add(fait.setMarginBottom(1).setMarginLeft(300));
                    document.add(new Paragraph("\n"+
                                            "\n"));
                    document.add(new Paragraph("Signature,nom et prénoms de l'Agent des douanes").setFontSize(12).setMarginBottom(0).setFont(font2).setMarginLeft(140).setBold());
                    
                    document.add(new Paragraph("\n"));
                    document.add(new Paragraph("NB: Cette fiche ne peut etre subtituée à aucun document administratif exigible par les Autorités du pays").setFontSize(12).setMarginBottom(5).setMarginLeft(12).setFont(font2).setBold());
                    document.add(new Paragraph("\n"+
                                                "\n"+
                                                "\n"));
                //Ajout du drapeau
                Image flag = new Image(ImageDataFactory.create("/home/<USER>/giiiit/erp-backend/transport/src/main/resources/static/images/flag.png"))
                    .setWidth(110)
                    .setHeight(20)
                    .setFixedPosition(240, 60);
                Paragraph flagHead = new Paragraph()
                    .add(flag);
                    document.add(flagHead);

                //Ajout de la signature
                Image signature = new Image(ImageDataFactory.create("/home/<USER>/giiiit/erp-backend/transport/src/main/resources/static/images/signature.png"))
                    .setWidth(220)
                    .setHeight(110)
                    .setFixedPosition(350, 40);
                Paragraph signHead = new Paragraph()
                        .add(signature);
                document.add(signHead);
                //Fermetture du document
                document.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    
        return new ByteArrayInputStream(out.toByteArray());
    }

}