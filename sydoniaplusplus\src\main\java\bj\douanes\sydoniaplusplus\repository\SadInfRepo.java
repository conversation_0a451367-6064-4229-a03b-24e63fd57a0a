package bj.douanes.sydoniaplusplus.repository;

import java.util.List;

import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.ListCrudRepository;
import org.springframework.data.repository.query.Param;

import bj.douanes.sydoniaplusplus.model.CshAllKeys;
import bj.douanes.sydoniaplusplus.model.SadInf;

public interface SadInfRepo extends ListCrudRepository<SadInf, CshAllKeys>{

    @Query("SELECT * FROM \"OPS$ASY\".SAD_INF WHERE SAD_TXT LIKE '%' || :chassis || '%'")
    List<SadInf> findBySadInfList(@Param("chassis") String chassis);

    @Query("SELECT * FROM \"OPS$ASY\".SAD_INF WHERE KEY_YEAR=:keyYear AND KEY_CUO=:keyCuo AND KEY_DEC=:keyDec AND <PERSON><PERSON>Y_NBER=:keyNber")
    List<SadInf> findByFourKeys(String keyYear, String keyCuo, String keyDec, String keyNber);
}
