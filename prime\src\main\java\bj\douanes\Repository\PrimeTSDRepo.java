package bj.douanes.Repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import bj.douanes.Model.PrimeTSD;

@Repository
public interface PrimeTSDRepo extends JpaRepository<PrimeTSD, Long> {
    
    //une requette pour recuperer la prime TSD par repartition
    // @Query("SELECT p FROM PrimeTSD p WHERE p.repartition.idRepartition = :idRepartition")
    // PrimeTSD findByRepartitionId(@Param("idRepartition") Long idRepartition);

    PrimeTSD findByRepartitionIdRepartition(Long idRepartition);
    


}
