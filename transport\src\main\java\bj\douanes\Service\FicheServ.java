package bj.douanes.Service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.stereotype.Service;

import bj.douanes.Model.Fiche;
import bj.douanes.Repo.FicheRepo;
import lombok.RequiredArgsConstructor;

public interface FicheServ {
    Fiche getFicheById(Long id);
    Fiche createFiche(Fiche fiche);
    List<Fiche> getAllFiche();
    Fiche update(Fiche fiche, Long id);
    public Map<String, Object> getInfoStatutByCriteria(String serialNumber);
}


@Service
@RequiredArgsConstructor
class InnerFicheServ implements FicheServ{

    private final FicheRepo ficheRepo;

    @Override
    public Fiche getFicheById(Long id) {
        return ficheRepo.findById(id).orElse(null);
    }

    @Override
    public Fiche createFiche(Fiche fiche) {
        return ficheRepo.save(fiche);
    }

    @Override
    public List<Fiche> getAllFiche() {
        return ficheRepo.findAll();
    }

    @Override
    public Fiche update(Fiche fiche, Long id) {
        Fiche ficheToUpdate = ficheRepo.findById(id).orElse(null);
        if(ficheToUpdate == null){
            return null;
        }
        ficheToUpdate.setCuoCode(fiche.getCuoCode());
        ficheToUpdate.setNumCamion(fiche.getNumCamion());
        ficheToUpdate.setNomConducteur(fiche.getNomConducteur());
        ficheToUpdate.setPrenomConducteur(fiche.getPrenomConducteur());
        ficheToUpdate.setNumPermisConducteur(fiche.getNumPermisConducteur());
        ficheToUpdate.setContact(fiche.getContact());
        ficheToUpdate.setNameOwner(fiche.getNameOwner());
        ficheToUpdate.setFirstnameOwner(fiche.getFirstnameOwner());
        ficheToUpdate.setContactOwner(fiche.getContactOwner());
        ficheToUpdate.setNombreSac100(fiche.getNombreSac100());
        ficheToUpdate.setNombreSac50(fiche.getNombreSac50());
        ficheToUpdate.setPoidsProduct(fiche.getPoidsProduct());
        ficheToUpdate.setCommuneProduct(fiche.getCommuneProduct());
        ficheToUpdate.setDestinationProduct(fiche.getDestinationProduct());
        return ficheRepo.save(ficheToUpdate);
        
    }

    //une fonction qui permet de supprimer une fiche
    public String deleteFiche(Long id) {
        ficheRepo.deleteById(id);
        return "Fiche supprimer";
    }

    @Override
    public Map<String, Object> getInfoStatutByCriteria(String serialNumber) {
        Map<String, Object> hmstatus = new LinkedHashMap<>();
        Optional<Fiche> fiche = ficheRepo.findBySerialNumber(serialNumber);
        if(fiche.isPresent()){
            hmstatus.put("status", "success");
            hmstatus.put("data", fiche.get());
        }else{
            hmstatus.put("status", "error");
            hmstatus.put("message", "Fiche not found");
        }
        return hmstatus;
    }
    
}