<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>bj.douanes</groupId>
		<artifactId>backend</artifactId>
		<version>2.0</version>
	</parent>

	<artifactId>core</artifactId>
	<version>${core.version}</version>
	<name>Configuration</name>
	<description>Configuration project</description>

	<properties>
		<jjwt-version>0.12.5</jjwt-version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>5.1.40	</version>
		</dependency>
		<!-- <dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<scope>runtime</scope>
		</dependency> -->
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<scope>runtime</scope>
			<version>42.3.5</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>mysql</groupId>-->
<!--			<artifactId>mysql-connector-java</artifactId>-->
<!--			<scope>runtime</scope>-->
<!--			&lt;!&ndash; <version>8.0.33</version> &ndash;&gt;-->
<!--			<version>5.1.40</version>-->
<!--		</dependency>-->
		<!-- <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>10.1.0.jre17-preview</version>
        </dependency> -->

		<!-- https://mvnrepository.com/artifact/io.jsonwebtoken/jjwt-impl -->
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-impl</artifactId>
			<version>${jjwt-version}</version>
			<scope>runtime</scope>
		</dependency>
		<!-- https://mvnrepository.com/artifact/io.jsonwebtoken/jjwt-api -->
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-api</artifactId>
			<version>${jjwt-version}</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/io.jsonwebtoken/jjwt-jackson -->
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-jackson</artifactId>
			<version>${jjwt-version}</version>
			<scope>runtime</scope>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>org.apache.poi</groupId>-->
<!--			<artifactId>poi</artifactId>-->
<!--			<version>5.2.3</version>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>org.apache.poi</groupId>-->
<!--			<artifactId>poi-ooxml</artifactId>-->
<!--			<version>5.2.3</version>-->
<!--		</dependency>-->

		<!-- <dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
		</dependency> -->
	</dependencies>

</project>
