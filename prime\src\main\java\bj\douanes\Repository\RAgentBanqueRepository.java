package bj.douanes.Repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import bj.douanes.DTO.BanqueInfoDTO;
import bj.douanes.DTO.BanqueInfoDTO2;
import bj.douanes.Model.RAgentBanque;
import bj.douanes.Model.RAgentBanqueId;

@Repository
public interface RAgentBanqueRepository extends JpaRepository<RAgentBanque, RAgentBanqueId>{
    
    @Query("SELECT ru FROM RAgentBanque ru WHERE ru.id.matricule = :matricule")
    List<RAgentBanque> findAllByMatricule(String matricule); 
    
    // Requette pour recuperer la banque d'un agent via son matricule
    @Query("SELECT new bj.douanes.DTO.BanqueInfoDTO(ru.id.codeBanque, ru.rib) FROM RAgentBanque ru WHERE ru.id.matricule = :matricule")
    List<BanqueInfoDTO> findBanqueInfosByMatricule(String matricule);

    @Query("SELECT new bj.douanes.DTO.BanqueInfoDTO2(ru.id.codeBanque, ru.rib, b.nomBanque) FROM RAgentBanque ru JOIN ru.banque b WHERE ru.id.matricule = :matricule")
    List<BanqueInfoDTO2> findBanqueInfosByMatricule2(String matricule);

}
