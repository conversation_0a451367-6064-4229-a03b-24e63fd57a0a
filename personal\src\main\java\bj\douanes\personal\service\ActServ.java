package bj.douanes.personal.service;
import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Actage;
import bj.douanes.personal.model.Agent;
import bj.douanes.personal.repository.ActageRepo;
import bj.douanes.personal.repository.AgentRepo;
import lombok.RequiredArgsConstructor;

public interface ActServ {
    
    Actage getActageById(Long id);
    Actage createActage(Long id,Actage actage);
    String deleteActage(Long id);
    List<Actage> getActageAllList();
}

@Service
@RequiredArgsConstructor
class ActServImlp implements ActServ{

    private final ActageRepo actageRepo;
    private final AgentRepo agentRepo;

    @Override
    public Actage getActageById(Long id) {
        return actageRepo.findById(id).orElse(null);
    }

    @Override
    public Actage createActage(Long id, Actage actage) {
        Agent agent = agentRepo.findById(id).orElse(null);
        actage.setAgent(agent);
        return actageRepo.save(actage);
    }
    
    @Override
    public String deleteActage(Long id) {
       actageRepo.deleteById(id);
       return "Act supprimer";
    }

    @Override
    public List<Actage> getActageAllList() {
       return actageRepo.findAll();
    }
   
    
}