package bj.douanes.DTO;

import java.math.BigDecimal;
import java.time.LocalDate;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrimeTSDUniteDto {
    private Long idPrimeUnite;
    private BigDecimal montantVerset;
    private BigDecimal oeuvreSocialUnite;
    private BigDecimal bonificationUnite;
    private BigDecimal partUnite;
    private BigDecimal cumulCoef;
    private LocalDate createdAt;
    private LocalDate updatedAt;
    private String codeUnite;
    private Long idRepartition;
}
