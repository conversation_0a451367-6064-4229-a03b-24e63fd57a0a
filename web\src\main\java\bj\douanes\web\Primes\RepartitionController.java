package bj.douanes.web.Primes;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.Model.Repartition;
import bj.douanes.facade.Prime.RepartitionFacade;
import bj.douanes.facade.UTILS.DTO;
import bj.douanes.utils.AppResponse;
import lombok.RequiredArgsConstructor;


@RestController
@RequiredArgsConstructor
@RequestMapping("api/prime/repartition")
public class RepartitionController {
    
    private final RepartitionFacade repartitionFacade;

    //afficher toutes les repartitions
    @GetMapping("/repartitions")
    public List<Repartition> getAllRepartitions() {
        return repartitionFacade.getAllRepartitions();
    }  
    
    //par id
    @GetMapping("/{id}")
    public Repartition getRepartitionById(@PathVariable Long id) {
        return repartitionFacade.getRepartitionById(id);
    }

    //creer une nouvelle repartition
    @PostMapping("/createRepartition")
    public ResponseEntity<?> createRepartition(@RequestBody DTO.RepartitionDto repartition) {
        return AppResponse.created(repartitionFacade.createRepartition(repartition));
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<?> updateRepartition(@RequestBody DTO.RepartitionDto repartition, @PathVariable Long id) {
        return AppResponse.ok(repartitionFacade.updateRepartition(id, repartition));
    }

    //si possible supprimer une repartition
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<?> deleteRepartition(@PathVariable Long id) {
        repartitionFacade.deleteRepartition(id);
        return AppResponse.ok("Repartition deleted successfully");
    }
}
