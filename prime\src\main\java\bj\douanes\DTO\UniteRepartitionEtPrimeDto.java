package bj.douanes.DTO;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UniteRepartitionEtPrimeDto {
    private String codeUnite;
    private Long idRepartition;

    // Champs pour PrimetsdUnite
    private BigDecimal montantVerset;
    private BigDecimal oeuvreSocialUnite;
    private BigDecimal bonificationUnite;
    private BigDecimal partUnite;
    private BigDecimal cumulCoef;
}
