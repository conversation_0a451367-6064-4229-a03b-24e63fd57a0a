package bj.douanes.sydoniaplusplus.repository;

import java.util.List;

import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.ListCrudRepository;

import bj.douanes.sydoniaplusplus.model.CshAllKeys;
import bj.douanes.sydoniaplusplus.model.CshGenCdi;

public interface CshGenCdiRepo extends ListCrudRepository<CshGenCdi, CshAllKeys> {

    @Query("SELECT * FROM (SELECT RCP_YEAR,RCP_CUO,RCP_SERIAL,RCP_NBER,CDI_DEC_STM,CDI_DECLARANT,CDI_DECL_TOT,CDI_DEC_IDENT,CDI_AMOUNT_TOTAL,CDI_RCPT_DATE,CDI_RCPT_TIME,CDI_TOTAL_DECL,CDI_TOTAL_OTHER,OPE_DAT,OPE_SFT,OPE_NBR  FROM \"OPS$ASY\".CSH_GEN_CDI \r\n" + //
            "UNION\r\n" + //
            "SELECT RCP_YEAR,RCP_CUO,RCP_SERIAL,RCP_NBER,DDI_DEC_STM,DDI_ACCOUNTNBER,DDI_DECL_TOT,DDI_DEC_IDENT,DDI_AMOUNT_TOTAL,DDI_RCPT_DATE,DDI_RCPT_TIME,DDI_TOTAL_DECL,DDI_TOTAL_OTHER,OPE_DAT,OPE_SFT,OPE_NBR  FROM \"OPS$ASY\".CSH_GEN_DDI) WHERE RCP_YEAR=:rcpYear AND RCP_CUO=:rcpCuo AND RCP_SERIAL=:rcpSerial AND RCP_NBER=:rcpNber")
    List<CshGenCdi> findByCshList(String rcpYear, String rcpCuo, String rcpSerial, String rcpNber);
}
