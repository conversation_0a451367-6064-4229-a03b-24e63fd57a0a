package bj.douanes.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.facade.MutationFacade;
import bj.douanes.facade.UTILS.DTO;
import bj.douanes.utils.AppResponse;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("api/agents/mutation")
public class MutationController {
    
    @Autowired
    private MutationFacade mutationFacade;

    @GetMapping
    public ResponseEntity<?> getAllMutationOrNomination() {
        return AppResponse.ok(mutationFacade.getMutationAllList());
    }

    @GetMapping("{id}")
    public ResponseEntity<?> getMutationOrNominationById(@PathVariable Long id) {
        return AppResponse.ok(mutationFacade.getMutationById(id));
    }

    @PostMapping("/{agentId}")
    public ResponseEntity<?> createMutationOrNomination(@RequestBody DTO.MutationDto mutationDto,@PathVariable Long agentId) {
        return AppResponse.created(mutationFacade.createMutation(agentId, mutationDto));
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<?> updateMutationOrNomination(@RequestBody DTO.MutationDto mutationDto,@PathVariable Long id){
        return AppResponse.ok(mutationFacade.update(id, mutationDto)); 
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<?> deleteMutationOrNomination(@PathVariable Long id){
        return AppResponse.ok(mutationFacade.deleteMutation(id)); 
    }
}
