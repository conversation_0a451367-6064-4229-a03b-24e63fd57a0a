package bj.douanes.sydoniaplusplus.model;


import java.math.BigDecimal;
import java.sql.Time;
import java.time.LocalDate;
import java.util.List;

import org.springframework.data.annotation.Transient;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "OPS$ASY", name = "CSH_GEN_CDI")
public class CshGenCdi {
    
    @Column("RCP_YEAR")
    private String rcpYear;

    @Column("RCP_CUO")
    private String rcpCuo;

    @Column("RCP_SERIAL")
    private String rcpSerial;

    @Column("RCP_NBER")
    private String rcpNber;

    @Column("CDI_DEC_STM")
    private String cdiDecStm;

    @Column("CDI_DECLARANT")
    private String cdiDeclarant;

    @Column("CDI_COMPANY")
    private String cdiCompany;

    @Column("CDI_DECL_TOT")
    private String cdiDeclTot;

    @Column("CDI_DEC_IDENT")
    private String cdiDecIdentString;

    @Column("CDI_AMOUNT_TOTAL")
    private BigDecimal cdiAmountTotal;

    @Column("CDI_RCPT_DATE")
    private LocalDate cdiRcptDate;
    
    @Column("CDI_RCPT_TIME")
    private Time cdiRcptTime;

    @Column("CDI_TOTAL_DECL")
    private BigDecimal cdiTotalDecl;

    @Column("CDI_TOTAL_OTHER")
    private String cdiTotalOther;

    @Column("OPE_DAT")
    private LocalDate opeDat;

    @Column("OPE_SFT")
    private String opeSft;

    @Column("OPE_NBR")
    private String opeNbr;
 
    @Transient
    private List<CshDec> cshDecList;

    @Transient
    private List<CshMea> cshMeaList;

    @Transient
    private UnCmpTab unCmpTab;

    @Transient
    private UnCuoTab unCuoTab;

}
