package bj.douanes.Services;

import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.DTO.BanqueDTO;
import bj.douanes.Model.Banque;
import bj.douanes.Repository.BanqueRepository;
import lombok.RequiredArgsConstructor;

public interface BanqueServ {
    
    List<BanqueDTO> getAllbanques();
    Banque createBanque(Banque banque);
    Banque updateBanque(String codeBanque, Banque banque);
    void deleteBanque(String codeBanque);
}

@Service
@RequiredArgsConstructor
class BanqueServImpl implements BanqueServ {

   private final BanqueRepository banqueRepository;

    @Override
    public List<BanqueDTO> getAllbanques() {
        return banqueRepository.findAll().stream()
            .map(banque -> new BanqueDTO(banque.getCodeBanque(), banque.getNomBanque(),
                banque.getCreatedAt(), banque.getUpdatedAt()))
            .toList();
    }

    @Override
    public Banque createBanque(Banque banque) {
        return banqueRepository.save(banque);
    }

    @Override
    public Banque updateBanque(String codeBanque, Banque banque) {
        Banque existingBanque = banqueRepository.findById(codeBanque)
            .orElseThrow(() -> new RuntimeException("Banque not found with code: " + codeBanque));
        
        existingBanque.setNomBanque(banque.getNomBanque());
        existingBanque.setUpdatedAt(banque.getUpdatedAt());
        
        return banqueRepository.save(existingBanque);
    }

    @Override
    public void deleteBanque(String codeBanque) {
        banqueRepository.deleteById(codeBanque);
    }
}