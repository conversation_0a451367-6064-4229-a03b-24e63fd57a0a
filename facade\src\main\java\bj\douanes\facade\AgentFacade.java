package bj.douanes.facade;

import java.util.List;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Agent;
import bj.douanes.personal.service.AgentServ;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface AgentFacade {
    
    List<Agent> getAllAgents();
    
    Agent getAgentById(Long id);
    
    Agent createAgent(Object agent);

    // Agent createSituationAdFnc(Long agentId,Object situationDto);
    
    Agent updateAgent(Long id, Object agent);
    
    String deleteAgent(Long id);
}

@Slf4j
@Service
@RequiredArgsConstructor
class AgentFacadeImpl implements AgentFacade {

    private final ModelMapper modelMapper;
    private final AgentServ agentService;
    

    @Override
    public List<Agent> getAllAgents() {
        log.info("Implemented method 'getAllAgents'");
        return agentService.getAllAgents();
    }
    @Override
    public Agent getAgentById(Long id) {
        log.info("Implemented method 'getUserAgentById'");
        return agentService.getAgentById(id);
    }
    @Override
    public Agent createAgent(Object agentDto) {
        log.info("Implemented method 'createAgent'");
        var newAgent = this.modelMapper.map(agentDto, Agent.class);
        return agentService.saveAgent(newAgent) ;
    }

    @Override
    public Agent updateAgent(Long id, Object agent) {
        log.info("Implementation de la mise a jour");
        Agent existingAgent = agentService.getAgentById(id);
        log.info("Avant le mapping: {}", existingAgent);
        if (agent != null) {
            modelMapper.map(agent, existingAgent);
        }
        log.info("Après le mapping", existingAgent);
        return agentService.saveAgent(existingAgent);
    }
    @Override
    public String deleteAgent(Long id) {
        log.info("Suppression de l'agent avec ID:", id);
            agentService.getAgentById(id);
            agentService.deleteAgent(id);
        return "Agent Supprimé avec success!!!!";
    }

}
