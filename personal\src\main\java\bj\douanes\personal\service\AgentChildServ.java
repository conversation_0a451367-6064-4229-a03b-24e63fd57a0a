package bj.douanes.personal.service;

import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Agent;
import bj.douanes.personal.model.AgentChildren;
import bj.douanes.personal.repository.AgentChildRepo;
import bj.douanes.personal.repository.AgentRepo;
import lombok.RequiredArgsConstructor;

public interface AgentChildServ {
    
    AgentChildren getChildById(Long id);
    AgentChildren createChild(Long agentId,AgentChildren child);
    String deleteChild(Long id);
    List<AgentChildren> getChildAllList();
    // AgentChildren update(Long id,AgentChildren child);
}

@Service
@RequiredArgsConstructor
class ChildrenImpl implements AgentChildServ{

    private final AgentChildRepo agentChildRepo;
    private final AgentRepo agentRepo;

    @Override
    public AgentChildren getChildById(Long id) {
       return agentChildRepo.findById(id).orElse(null);
    }
    @Override
    public AgentChildren createChild(Long agentId, AgentChildren child) {
        Agent agent = agentRepo.findById(agentId).orElse(null);
        child.setAgent(agent);
        return agentChildRepo.save(child);
    }
    @Override
    public String deleteChild(Long id) {
        agentChildRepo.deleteById(id);
        return "Child deleted";
    }
    @Override
    public List<AgentChildren> getChildAllList() {
       return agentChildRepo.findAll();
    }
   
    
}