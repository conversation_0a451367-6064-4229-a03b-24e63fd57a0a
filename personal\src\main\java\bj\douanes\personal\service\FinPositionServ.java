package bj.douanes.personal.service;

import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Agent;
import bj.douanes.personal.model.FinPosition;
import bj.douanes.personal.repository.AgentRepo;
import bj.douanes.personal.repository.FinPositionRepo;
import lombok.RequiredArgsConstructor;

public interface FinPositionServ {
    
    FinPosition getFinPositionById(Long id);
    FinPosition createFinPosition(Long id,FinPosition finPosition);
    String deleteFinPosition(Long id);
    List<FinPosition> getFinPositionAllList();
}

@Service
@RequiredArgsConstructor
class FinPositionImpl implements FinPositionServ{

    private final AgentRepo agentRepo;
    private final FinPositionRepo finPositionRepo;
    
    @Override
    public FinPosition getFinPositionById(Long id) {
        return finPositionRepo.findById(id).orElse(null);
    }

    @Override
    public FinPosition createFinPosition(Long id, FinPosition finPosition) {
        Agent agent = agentRepo.findById(id).orElse(null);
        finPosition.setAgent(agent);
        return finPositionRepo.save(finPosition);
    }

    @Override
    public String deleteFinPosition(Long id) {
       finPositionRepo.deleteById(id);
       return "Fin deb situation supprimer";
    }

    @Override
    public List<FinPosition> getFinPositionAllList() {
        return finPositionRepo.findAll();
    }
    
}