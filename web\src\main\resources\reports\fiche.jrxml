<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.21.2.final using JasperReports Library version 3.5.3  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="fiche" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="cuoCode" class="java.lang.String"/>
	<field name="produit" class="java.lang.String"/>
	<field name="numCamion" class="java.lang.String"/>
	<field name="nomConducteur" class="java.lang.String"/>
	<field name="prenomConducteur" class="java.lang.String"/>
	<field name="numPermisConducteur" class="java.lang.String"/>
	<field name="contact" class="java.lang.String"/>
	<field name="nameOwner" class="java.lang.String"/>
	<field name="firstnameOwner" class="java.lang.String"/>
	<field name="contactOwner" class="java.lang.String"/>
	<field name="nombreSac100" class="java.lang.Integer"/>
	<field name="nombreSac50" class="java.lang.Integer"/>
	<field name="poidsProduct" class="java.lang.Integer"/>
	<field name="communeProduct" class="java.lang.String"/>
	<field name="destinationProduct" class="java.lang.String"/>
	<field name="dateAsDate" class="java.util.Date"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="770" splitType="Stretch">
			<image>
				<reportElement x="-11" y="11" width="561" height="758" backcolor="#FFFFFF"/>
				<imageExpression class="java.lang.String"><![CDATA["/home/<USER>/giiiit/erp-backend/web/src/main/resources/reports/fiche.jpg"]]></imageExpression>
			</image>
			<textField>
				<reportElement mode="Opaque" x="156" y="135" width="163" height="22" backcolor="#FFFFFF"/>
				<textElement lineSpacing="Single">
					<font fontName="Verdana" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cuoCode}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="125" y="172" width="124" height="20" backcolor="#FFFFFF"/>
				<textElement lineSpacing="Single">
					<font fontName="Verdana" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{produit}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="101" y="210" width="159" height="20" backcolor="#FFFFFF"/>
				<textElement lineSpacing="Single">
					<font fontName="Verdana" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{numCamion}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="212" y="250" width="98" height="22" backcolor="#FFFFFF"/>
				<textElement lineSpacing="Single">
					<font fontName="Verdana" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomConducteur}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="310" y="250" width="184" height="16" backcolor="#FFFFFF"/>
				<textElement lineSpacing="Single">
					<font fontName="Verdana" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{prenomConducteur}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="90" y="287" width="139" height="15" backcolor="#FFFFFF"/>
				<textElement lineSpacing="Single">
					<font fontName="Verdana" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contact}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="182" y="325" width="188" height="20" backcolor="#FFFFFF"/>
				<textElement lineSpacing="Single">
					<font fontName="Verdana" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{numPermisConducteur}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="283" y="365" width="87" height="18" backcolor="#FFFFFF"/>
				<textElement lineSpacing="Single">
					<font fontName="Verdana" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nameOwner}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="370" y="365" width="148" height="19" backcolor="#FFFFFF"/>
				<textElement lineSpacing="Single">
					<font fontName="Verdana" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{firstnameOwner}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="88" y="401" width="142" height="17" backcolor="#FFFFFF"/>
				<textElement lineSpacing="Single">
					<font fontName="Verdana" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contactOwner}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="175" y="439" width="112" height="22" backcolor="#FFFFFF"/>
				<textElement lineSpacing="Single">
					<font fontName="Verdana" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nombreSac100}+" sac/100"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="285" y="439" width="108" height="23" backcolor="#FFFFFF"/>
				<textElement lineSpacing="Single">
					<font fontName="Verdana" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nombreSac50}+" sac/50"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="394" y="439" width="94" height="25" backcolor="#FFFFFF"/>
				<textElement lineSpacing="Single">
					<font fontName="Verdana" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{poidsProduct}+" kg"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="101" y="477" width="128" height="16" backcolor="#FFFFFF"/>
				<textElement lineSpacing="Single">
					<font fontName="Verdana" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{communeProduct}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="88" y="514" width="148" height="20" backcolor="#FFFFFF"/>
				<textElement lineSpacing="Single">
					<font fontName="Verdana" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{destinationProduct}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="316" y="553" width="86" height="21" backcolor="#FFFFFF"/>
				<textElement lineSpacing="Single">
					<font fontName="Verdana" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dateAsDate}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="320" y="134" width="172" height="23"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="238" y="172" width="255" height="20"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="250" y="210" width="243" height="22"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="229" y="287" width="259" height="16"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="369" y="326" width="119" height="19"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="230" y="401" width="258" height="22"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="229" y="476" width="264" height="22"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="230" y="512" width="260" height="22"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="398" y="553" width="90" height="22"/>
				<textElement lineSpacing="Single"/>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</title>
</jasperReport>
