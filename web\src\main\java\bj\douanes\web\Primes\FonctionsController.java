package bj.douanes.web.Primes;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.DTO.FonctionsDTO;
import bj.douanes.facade.Prime.FonctionFacade;
import bj.douanes.utils.AppResponse;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("api/prime/fonctions")
public class FonctionsController {
    
    private final FonctionFacade fonctionFacade;

    @GetMapping("/all")
    public ResponseEntity<?> getAllFonctions() {
        return AppResponse.ok(fonctionFacade.getAllFonctions());
    }

    @GetMapping("/{codeFonction}")
    public ResponseEntity<?> getFonctionById(@PathVariable String codeFonction) {
        return AppResponse.ok(fonctionFacade.getFonctionById(codeFonction));
    }

    @PostMapping("/create")
    public ResponseEntity<?> createFonction(@RequestBody FonctionsDTO fonctionDTO) {
        return AppResponse.created(fonctionFacade.createFonction(fonctionDTO));
    }

    @PutMapping("/update/{codeFonction}")
    public ResponseEntity<?> updateFonction(@PathVariable String codeFonction, @RequestBody FonctionsDTO fonctionDTO) {
        return AppResponse.ok(fonctionFacade.updateFonction(codeFonction, fonctionDTO));
    }

    @DeleteMapping("/delete/{codeFonction}")
    public ResponseEntity<?> deleteFonction(@PathVariable String codeFonction) {
        fonctionFacade.deleteFonction(codeFonction);
        return AppResponse.noContent();
    }

    
}
