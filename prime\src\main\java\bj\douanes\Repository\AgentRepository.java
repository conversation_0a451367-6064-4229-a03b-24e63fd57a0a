package bj.douanes.Repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import bj.douanes.Model.AgentTSD;

@Repository
public interface AgentRepository extends JpaRepository<AgentTSD, String>{

    // Requete pour afficher tout les agents qui sont actifs
    List<AgentTSD> findAllByActifTrue();

    // @Query("SELECT a FROM AgentTSD a LEFT JOIN FETCH a.unites u LEFT JOIN FETCH a.fonctions f LEFT JOIN FETCH a.banques b")
    // List<AgentTSD> findAllAgentsWithDto();


    
    // Requete pour afficher tout les agents ainsi que leurs rattachements unite,fonction et banque
    // @Query("SELECT DISTINCT a FROM AgentTSD a " +
    //    "LEFT JOIN FETCH a.unites u " +
    //    "LEFT JOIN FETCH u.unite " +
    //    "LEFT JOIN FETCH a.fonctions f " +
    //    "LEFT JOIN FETCH f.fonction " +
    //    "LEFT JOIN FETCH a.banques b " +
    //    "LEFT JOIN FETCH b.banque")
    // List<AgentCreationDto> findAllAgentsWithDetails();

        // Requete pour afficher les agents par unite
    //     @Query("SELECT DISTINCT a FROM AgentTSD a " +
    //     "LEFT JOIN FETCH a.unites u " +
    //     "LEFT JOIN FETCH u.unite " +
    //     "WHERE u.unite.codeUnite = :codeUnite")
    // List<AgentTSD> findAllAgentsByUnite(String codeUnite);

        // Requete pour afficher les agents par fonction
    //     @Query("SELECT DISTINCT a FROM AgentTSD a " +
    //     "LEFT JOIN FETCH a.fonctions f " +
    //     "LEFT JOIN FETCH f.fonction " +
    //     "WHERE f.fonction.codeFonction = :codeFonction")
    // List<AgentTSD> findAllAgentsByFonction(String codeFonction);

        // Requete pour afficher les agents par banque
    //     @Query("SELECT DISTINCT a FROM AgentTSD a " +
    //     "LEFT JOIN FETCH a.banques b " +
    //     "LEFT JOIN FETCH b.banque " +
    //     "WHERE b.banque.codeBanque = :codeBanque")
    // List<AgentTSD> findAllAgentsByBanque(String codeBanque);


    
    

}
