package bj.douanes.web.Primes;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.DTO.MontantCollectParUniteDto;
import bj.douanes.facade.Prime.PrimeTSDUniteFacade;
import bj.douanes.facade.Prime.PrimeTsdAgentFacade;
import bj.douanes.utils.AppResponse;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("api/prime/tsd-unite")
public class PrimeTSDUniteController {
    
    private final PrimeTSDUniteFacade primeTSDUniteFacade;
    private final PrimeTsdAgentFacade primeTsdAgentFacade;


    //api pour afficher les montants collectes par unite et les calcules des parts par id
    @GetMapping("/repartition/{idRepartition}")
    public ResponseEntity<?> getAllByIdRepartition(@PathVariable Long idRepartition) {
        return AppResponse.ok(primeTSDUniteFacade.findAllByIdRepartition(idRepartition));
    }
       

    //api pour charger les montants collectes par unite
    @PostMapping("/unite-montant-collectez")
    public ResponseEntity<?> createUniteEtMontantCollectez(@RequestBody List<MontantCollectParUniteDto> dto,@RequestParam Long idRepartition) {
        if (dto == null || dto.isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        return AppResponse.created(primeTSDUniteFacade.createVersementByUnite(dto, idRepartition));
    }

    //api pour determiner les part des agents jusqu'au its et le net a payer
    @PostMapping("/create")
    public ResponseEntity<?> createPrimeTsdAgent(@RequestParam Long idRepartition) {
        return AppResponse.created(primeTsdAgentFacade.createPrimeTsdAgent(idRepartition));
    }

    //api pour afficher les calcules globaux montant par unite
    @GetMapping("/primestsd")
    public ResponseEntity<?> getAllPrimes() {
        return AppResponse.ok(primeTSDUniteFacade.findAllPrimes());
    }

    //et par id repartition
    @GetMapping("/primestsd/{id}")
    public ResponseEntity<?> getPrimeTSDById(@PathVariable Long id) {
        return AppResponse.ok(primeTSDUniteFacade.getPrimeTSDById(id));
    }
}
