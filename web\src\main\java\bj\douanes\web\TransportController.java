package bj.douanes.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.Model.Fiche;
import bj.douanes.facade.FicheFacade;

@RestController
@RequestMapping("api/transport")
public class TransportController {
    
    @Autowired
    private FicheFacade ficheFacade;
    
    // @Autowired
    // private FicheReportService ficheReportService;

    //Recuperation de toutes les fiches
    @GetMapping("/all")
    public ResponseEntity<?> getAllTransportFiche() {
        return ResponseEntity.ok(ficheFacade.getAllFiche());
    }

    //Recuperation selon l'id
    @GetMapping("/{id}")
    public ResponseEntity<?> getTransportFiche(@PathVariable Long id) {
        Fiche myFiche = ficheFacade.getFicheById(id);
        
        if (myFiche == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(" La Fiche avec l'ID " + id + " n'a pas été trouvée.");
        }
        return ResponseEntity.ok(myFiche);
    }

    // Création d'une fiche transport (1ere partie)
    @PostMapping("/create")
    public ResponseEntity<Fiche> createTransportFicheAndGenerateReport(@RequestBody Fiche fiche) {
        try {
            // Créer l'objet Fiche
            Fiche createdFiche = ficheFacade.createFiche(fiche);
    
            // Si la création a échoué
            if (createdFiche == null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(null);
            }
            return ResponseEntity.ok()
                    .body(createdFiche);
    
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    //Creation du controller pour mettre a jour la fiche
    @PutMapping("/update/{id}")
    public ResponseEntity<?> updateTransportFiche(@RequestBody Fiche fiche, @PathVariable Long id) {
        Fiche updatedFiche = ficheFacade.updateFiche(fiche, id);
        if (updatedFiche == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body("La Fiche avec l'ID " + id + " n'a pas été trouvée.");
        }
        return ResponseEntity.ok(updatedFiche);
    }


    //telechargement du pdf (2eme partie)
    @GetMapping("/download/{id}")
    public ResponseEntity<InputStreamResource> downloadTransportFicheReport(@PathVariable Long id) {
        try {
            InputStreamResource resource = ficheFacade.generatePdf(id);
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=Fiche_de_convoyage.pdf")
                    .contentType(MediaType.APPLICATION_PDF)
                    .body(resource);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    //Controller pour faire la recherche via le numero de serie
    @GetMapping("/find/{serialNumber}")
    public ResponseEntity<?> getTransportFicheBySerialNumber(@PathVariable String serialNumber) {
        return ResponseEntity.ok(ficheFacade.getFicheBySerialNumber(serialNumber));
    }




    // @PostMapping("/create")
    // public ResponseEntity<byte[]> createTransportFicheAndGenerateReport(@RequestBody Fiche fiche) {
    //     try {
    //         // Créer l'objet Fiche
    //         Fiche createdFiche = ficheFacade.createFiche(fiche);
    
    //         // Vérifier si la création a échoué
    //         if (createdFiche == null) {
    //             return ResponseEntity.status(HttpStatus.BAD_REQUEST)
    //                     .body(null);
    //         }
    
    //         // Générer le rapport PDF à partir de l'objet créé
    //         ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    //         ficheReportService.exportFicheReport(createdFiche, outputStream);
    
    //         // Retourner le rapport PDF dans la réponse
    //         return ResponseEntity.ok()
    //                 .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=fiche_report.pdf")
    //                 .contentType(MediaType.APPLICATION_PDF)
    //                 .body(outputStream.toByteArray());
    
    //     } catch (Exception e) {
    //         e.printStackTrace();
    //         return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
    //     }
    // }
}
