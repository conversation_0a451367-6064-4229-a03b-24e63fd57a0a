package bj.douanes.sydoniaplusplus.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.*;

import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "OPS$ASY", name = "UNCUOTAB")
public class UnCuoTab {

    @Column("CUO_COD") @Id
    private String cuoCod;

    @Column("CUO_NAM")
    private String cuoNam;

    @Column("CUO_ADR")
    private String cuoAdr;

    @Column("CUO_AD2")
    private String cuoAd2;

}
