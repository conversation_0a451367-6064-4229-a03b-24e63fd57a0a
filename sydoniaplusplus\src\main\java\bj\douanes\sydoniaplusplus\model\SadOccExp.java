package bj.douanes.sydoniaplusplus.model;

import java.math.BigDecimal;

import org.springframework.data.relational.core.mapping.*;

import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "OPS$ASY", name = "SAD_OCC_EXP")
public class SadOccExp {
    
    @Column("KEY_YEAR") @JsonIgnore
    private String keyYear;
    
    @Column("KEY_CUO") @JsonIgnore
    private String keyCuo;
    
    @Column("KEY_DEC") @JsonIgnore
    private String keyDec;
    
    @Column("KEY_NBER") @JsonIgnore
    private String keyNber;

    @Column("SAD_NUM") @JsonIgnore
    private BigDecimal sadNum;
    
    @Column("SAD_EXP_NAM")
    private String sadExpNam;
    
    @Column("SAD_EXP_ADD1")
    private String sadExpAdd1;
    
    @Column("SAD_EXP_ADD2")
    private String sadExpAdd2;

}
