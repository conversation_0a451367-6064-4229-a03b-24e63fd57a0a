package bj.douanes.Services;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import bj.douanes.DTO.OrdreVirementDto;

public class OrdreVirementGenerator {

    public static byte[] generateOrdreVirementExcel(List<OrdreVirementDto> ordreVirementList) throws IOException{
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Agents");
            int rowIdx = 0;

            // Écriture de l’en-tête
            Row header = sheet.createRow(rowIdx++);
            String[] columns = {
                "N°","Matricule","Statut", "Nom et Prénoms","Qualification_Fonction","Departement","Activité","Montant", 
                "RIB","Banque", "Code Banque","periode","annee","Type Prime" 
            };
            for (int i = 0; i < columns.length; i++) {
                header.createCell(i).setCellValue(columns[i]);
            }

            // Écriture des données
            for (OrdreVirementDto ordre : ordreVirementList) {
                Row row = sheet.createRow(rowIdx++);
                row.createCell(0).setCellValue(rowIdx - 1); // N° de ligne autogénéré
                row.createCell(1).setCellValue(ordre.getMatricule());
                row.createCell(2).setCellValue("AE");
                row.createCell(3).setCellValue(ordre.getNom()+" "+ordre.getPrenoms());
                row.createCell(4).setCellValue("Fonctionnaire des douanes");
                row.createCell(5).setCellValue("Littoral");
                row.createCell(6).setCellValue("Exécution de services");
                row.createCell(7).setCellValue(ordre.getMontantNetPayer() != null ? ordre.getMontantNetPayer().doubleValue() : 0);
                row.createCell(8).setCellValue(ordre.getRib());
                row.createCell(9).setCellValue(ordre.getNomBanque());
                row.createCell(10).setCellValue(ordre.getCodeBanque());
                row.createCell(11).setCellValue(ordre.getPeriode());
                row.createCell(12).setCellValue(ordre.getAnnee());
                row.createCell(13).setCellValue(ordre.getTypePrime());
            }

            // Ajustement automatique de la largeur des colonnes
            for (int i = 0; i < columns.length; i++) {
                sheet.autoSizeColumn(i);
            }

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            return out.toByteArray();
        }
    }
}
