package bj.douanes.sydoniaplusplus.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.springframework.data.annotation.Transient;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "OPS$ASY", name = "SAD_ITM")
public class SadItm {

    @Column("KEY_YEAR") @JsonIgnore
    private String keyYear;
    
    @Column("KEY_CUO") @JsonIgnore
    private String keyCuo;
    
    @Column("KEY_DEC") @JsonIgnore
    private String keyDec;
    
    @Column("KEY_NBER") @JsonIgnore
    private String keyNber;

    @Column("ITM_NBER")
    private BigDecimal itmNber;
    
    @Column("SAD_NUM") 
    private BigDecimal sadNum;

    @Column("SADITM_PACK_MARK1")
    private String saditmPackMark1;

    @Column("SADITM_PACK_MARK2")
    private String saditmPackMark2;

    @Column("SADITM_HS_COD")
    private String saditmHsCod;

    @Column("SADITM_HSPREC_COD")
    private String saditmHsprecCod;

    @Column("SADITM_UNDEFINED1")
    private String saditmUndefined1;

    @Column("SADITM_UNDEFINED2")
    private String saditmUndefined2;

    @Column("SADITM_TAR_SPC")
    private String saditmTarSpc;

    @Column("SADITM_GOODS_DESC1")
    private String saditmGoodsDesc1;

    @Column("SADITM_GOODS_DESC2")
    private String saditmGoodsDesc2;

    @Column("SADITM_GOODS_DESC3")
    private String saditmGoodsDesc3;

    @Column("SADITM_PACK_NBER")
    private BigDecimal saditmPackNber;

    @Column("SADITM_PACK_KNDCOD")
    private String saditmPackKndcod;

    @Column("SADITM_CTY_ORIGCOD")
    private String saditmCtyOrigcod;

    @Column("SADITM_CTY_ORIGREG")
    private String saditmCtyOrigreg;

    @Column("SADITM_GROSS_MASS")
    private BigDecimal saditmGrossMass;

    @Column("SADITM_PREFER_COD")
    private String saditmPreferCod;

    @Column("SADITM_CTNR_NBER1")
    private String saditmCtnrNber1;

    @Column("SADITM_CTNR_NBER2")
    private String saditmCtnrNber2;

    @Column("SADITM_CTNR_NBER3")
    private String saditmCtnrNber3;

    @Column("SADITM_CTNR_NBER4")
    private String saditmCtnrNber4;

    @Column("SADITM_EXTD_PROC")
    private String saditmExtdProc;

    @Column("SADITM_NAT_PROC")
    private String saditmNatProc;

    @Column("SADITM_NET_MASS")
    private BigDecimal saditmNetMass;

    @Column("SADITM_QUOTA")
    private String saditmQuota;

    @Column("SADITM_TRSP_DOC")
    private String saditmTrspDoc;

    @Column("SADITM_SUPP_UNITS")
    private BigDecimal saditmSuppUnits;

    @Column("SADITM_ITM_PRICE")
    private BigDecimal saditmItmPrice;

    @Column("SADITM_VAL_METHOD")
    private String saditmValMethod;

    @Column("SADITM_LIC_NBER")
    private String saditmLicNber;

    @Column("SADITM_DVAL_AMOUNT")
    private BigDecimal saditmDvalAmount;

    @Column("SADITM_DQTY")
    private BigDecimal saditmDqty;

    @Column("SADITM_VALUE_DTAIL")
    private String saditmValueDtail;

    @Column("SADITM_AI_COD")
    private String saditmAiCod;

    @Column("SADITM_ADJ_RATE")
    private BigDecimal saditmAdjRate;

    @Column("SADITM_PREV_DOC")
    private String saditmPrevDoc;

    @Column("SADITM_PREV_WHSCOD")
    private String saditmPrevWhscod;

    @Column("SADITM_FREE_TEXT")
    private String saditmFreeText;

    @Column("SADITM_RESERVED")
    private String saditmReserved;

    @Column("SADITM_STAT_VAL")
    private BigDecimal saditmStatVal;

    @Column("SADITM_ITM_TOTAMT")
    private BigDecimal saditmItmTotamt;

    @Column("SADITM_ITM_TOTMOP")
    private String saditmItmTotmop;

    @Column("SADITM_ATT_DOCUMEN")
    private String saditmAttDocumen;

    @Column("SADITM_SUP_VAL1")
    private BigDecimal saditmSupVal1;

    @Column("SADITM_SUP_VAL2")
    private BigDecimal saditmSupVal2;

    @Transient
    private List<SadTax> sadTaxList;

    @Transient
    private UnPkgTab unPkgTab;

    public void addSadTax(SadTax sadTax) {
        if (sadTaxList == null) sadTaxList = new ArrayList<>();
        sadTaxList.add(sadTax);
    }

    // Getters and setters
}
