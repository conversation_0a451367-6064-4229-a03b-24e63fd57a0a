<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>bj.douanes</groupId>
		<artifactId>backend</artifactId>
		<version>2.0</version>
	</parent>

	<artifactId>sydoniaplusplus</artifactId>
	<version>${sydoniaplusplus.version}</version>
	<name>Sydoniaplusplus</name>
	<description>Sydoniaplusplus project</description>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc8</artifactId>
			<!-- <version>********</version> -->
		</dependency>
	</dependencies>

</project>
