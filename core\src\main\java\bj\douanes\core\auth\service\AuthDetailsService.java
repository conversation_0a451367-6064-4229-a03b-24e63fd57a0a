package bj.douanes.core.auth.service;

import java.util.Optional;

import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import bj.douanes.core.auth.model.User;
import bj.douanes.core.auth.repository.UserRepo;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class AuthDetailsService implements UserDetailsService {

    private final UserRepo userRepo;

    @Override
    public UserDetails loadUserByUsername(String email) throws UsernameNotFoundException {
        Optional<User> optionalUser=  userRepo.findByEmail(email);
        return optionalUser.orElseThrow(
            () -> new UsernameNotFoundException("User Not Found.")
        );
    }
    
}
