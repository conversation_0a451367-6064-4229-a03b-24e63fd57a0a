package bj.douanes.facade;

import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.Map;

import org.springframework.core.io.InputStreamResource;
import org.springframework.stereotype.Service;

import bj.douanes.Model.Fiche;
import bj.douanes.Service.FicheServ;
import bj.douanes.Service.PdfService;
import lombok.RequiredArgsConstructor;

public interface FicheFacade {

    List<Fiche> getAllFiche();
    Fiche getFicheById(Long id);
    Fiche createFiche(Fiche fiche);
    Fiche updateFiche(Fiche fiche, Long id);
    Map<String, Object> getFicheBySerialNumber(String serialNumber);
    InputStreamResource generatePdf(Long id);
        
}

@Service
@RequiredArgsConstructor
class FichefacadeImpl implements FicheFacade{

    
    private final FicheServ ficheServ;
    private final PdfService pdfService;
    // private final ModelMapper modelMapper;

    @Override
    public Fiche getFicheById(Long id) {
        return ficheServ.getFicheById(id);
    }

    @Override
    public Fiche createFiche(Fiche fiche) {
        return ficheServ.createFiche(fiche);     
    }

    //creation d'une methode de mise a jour
    @Override
    public Fiche updateFiche(Fiche fiche, Long id) {
        return ficheServ.update(fiche, id);
    }

    //creation d'une methode de generation de pdf
    @Override
    public InputStreamResource generatePdf(Long id) {
        Fiche fiche = ficheServ.getFicheById(id);
        ByteArrayInputStream pdfStream = pdfService.generatePdf(fiche);
        return new InputStreamResource(pdfStream);
    }

    @Override
    public List<Fiche> getAllFiche() {
        return ficheServ.getAllFiche();
    }

    @Override
    public Map<String, Object> getFicheBySerialNumber(String serialNumber) {
        return ficheServ.getInfoStatutByCriteria(serialNumber);
    }



    
}