package bj.douanes.core.config.security;

import java.io.IOException;

import org.springframework.http.HttpHeaders;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import bj.douanes.core.auth.service.AuthDetailsService;
import bj.douanes.core.shared.jwt.JwtUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class WebRequestFilter extends OncePerRequestFilter {

    private final JwtUtils jwtUtils;
    private final AuthDetailsService authDetailsService;

    @Override
    // @Slf4j
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {


        String path = request.getRequestURI().toLowerCase();
//        logger.info("SpaWebFilter path: " + path);

        /*if (!path.equals("/") &&
                !path.startsWith("/api") &&
                !path.startsWith("/swagger") &&
                !path.startsWith("/static") &&
                !path.startsWith("/h2-console") &&
                !path.startsWith("/v3") &&
                !path.startsWith("/manifest.json") &&
                !path.startsWith("/favicon.ico") &&
                !path.startsWith("/robots.txt") &&
                !path.endsWith(".xml") &&
                !path.endsWith(".json") &&
                !path.endsWith(".jpg") &&
                !path.endsWith(".jpeg") &&
                !path.endsWith(".gif") &&
                !path.endsWith(".png")) {*/
        if (path.startsWith("/eservice")) {
//            logger.info("SpaWebFilter forwarding to /index.html from path: " + path);
            request.getRequestDispatcher("/index.html").forward(request, response);
            return;
        }

        final String authHeader = request.getHeader(HttpHeaders.AUTHORIZATION);

        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            final String jwt = authHeader.substring("Bearer ".length());
            final String username = jwtUtils.extractUsername(jwt);

            if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                UserDetails userDetails = authDetailsService.loadUserByUsername(username);
                if (jwtUtils.isTokenValide(jwt, userDetails)) {
                    final UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                        userDetails,
                        null, 
                        userDetails.getAuthorities()
                    );

                    authToken.setDetails(
                        new WebAuthenticationDetailsSource().buildDetails(request)
                    );

                    SecurityContextHolder.getContext().setAuthentication(authToken);
                    
                }
            }

        }

//        logger.info("SpaWebFilter sent along its way path: " + path);
        filterChain.doFilter(request, response);

    }  

}