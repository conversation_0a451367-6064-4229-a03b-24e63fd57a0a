package bj.douanes.facade;

import java.util.List;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import bj.douanes.personal.model.MutationNom;
import bj.douanes.personal.service.MutationServ;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface MutationFacade {
    
    MutationNom getMutationById(Long id);
    MutationNom createMutation(Long agentId,Object mutation);
    String deleteMutation(Long id);
    List<MutationNom> getMutationAllList();
    MutationNom update(Long id,Object mutation);
}

@Slf4j
@Service
@RequiredArgsConstructor
class MutationfacadeImpl implements MutationFacade{
    
    private final ModelMapper modelMapper;
    private final MutationServ mutationServ;

    @Override
    public MutationNom getMutationById(Long id) {
        return mutationServ.getMutationById(id);
    }

    @Override
    public MutationNom createMutation(Long agentId, Object mutation) {
        var newMutation = modelMapper.map(mutation, MutationNom.class);
        return mutationServ.createMutation(agentId, newMutation);
    }

    @Override
    public String deleteMutation(Long id) {
      return mutationServ.deleteMutation(id);
    }

    @Override
    public List<MutationNom> getMutationAllList() {
        return mutationServ.getMutationAllList();
    }

    @Override
    public MutationNom update(Long id, Object mutation) {
        log.info("Mise a jour de Mutation");
        MutationNom mutation2 = mutationServ.getMutationById(id);
        log.info("Avant le mapping: {}", mutation2);
        modelMapper.map(mutation,mutation2);
        return mutationServ.createMutation(id, mutation2);
    }
    
}