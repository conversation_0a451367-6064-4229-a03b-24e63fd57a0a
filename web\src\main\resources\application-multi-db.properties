# Configuration multi-datasources exemple
server.port=8082

# Définition des datasources disponibles
app.datasource.type.names=primary,transport_db,oracle_db

# Primary DB (PostgreSQL) - pour core, personal, prime
app.datasource.primary.url=*****************************************
app.datasource.primary.username=postgres
app.datasource.primary.password=password
app.datasource.primary.driver-class-name=org.postgresql.Driver
app.datasource.primary.dialect=org.hibernate.dialect.PostgreSQLDialect

# Transport DB (PostgreSQL séparée)
app.datasource.transport_db.url=**********************************************
app.datasource.transport_db.username=postgres
app.datasource.transport_db.password=password
app.datasource.transport_db.driver-class-name=org.postgresql.Driver
app.datasource.transport_db.dialect=org.hibernate.dialect.PostgreSQLDialect

# Oracle DB (pour sydoniaplusplus)
app.datasource.oracle_db.url=***********************************
app.datasource.oracle_db.username=hr
app.datasource.oracle_db.password=password
app.datasource.oracle_db.driver-class-name=oracle.jdbc.OracleDriver
app.datasource.oracle_db.dialect=org.hibernate.dialect.OracleDialect

# Configuration du mapping package -> datasource
app.datasource.mapping.default-data-source=primary
app.datasource.mapping.packages.bj.douanes.personal=primary
app.datasource.mapping.packages.bj.douanes.transport=transport_db
app.datasource.mapping.packages.bj.douanes.prime=primary
app.datasource.mapping.packages.bj.douanes.sydoniaplusplus=oracle_db
app.datasource.mapping.packages.bj.douanes.core=primary
