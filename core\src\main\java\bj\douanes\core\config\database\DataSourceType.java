package bj.douanes.core.config.database;

import java.util.List;

public final class DataSourceType {

    private List<String> envs;
    
    public final String first;

    public DataSourceType(List<String> newEnv) {
        this.envs = newEnv;
        this.first = this.envs.get(0);
    }

    public List<String> getEnvs() {
        return this.envs;
    }

    public String setName(String name) {
        return (this.envs.contains(name))? name: this.first;
    }
}