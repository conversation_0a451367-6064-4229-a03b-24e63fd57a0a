package bj.douanes.personal.service;

import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Agent;
import bj.douanes.personal.model.AgentConjoint;
import bj.douanes.personal.repository.AgentConjointRepo;
import bj.douanes.personal.repository.AgentRepo;
import lombok.RequiredArgsConstructor;

public interface AgentConjointServ {
    
    AgentConjoint getConjointById(Long id);
    AgentConjoint createConjoint(Long agentId,AgentConjoint conjoint);
    String deleteConjoint(Long id);
    List<AgentConjoint> getConjointAllList();
}

@Service
@RequiredArgsConstructor
class AgentConjointImpl implements AgentConjointServ{

    private final AgentConjointRepo agentConjointRepo;
    private final AgentRepo agentRepo;
    
    @Override
    public AgentConjoint getConjointById(Long id) {
        return agentConjointRepo.findById(id).orElse(null);
    }

    @Override
    public AgentConjoint createConjoint(Long agentId,AgentConjoint conjoint) {
        Agent agent = agentRepo.findById(agentId).orElse(null);
        conjoint.setAgent(agent);
        return agentConjointRepo.save(conjoint);
    }
    @Override
    public String deleteConjoint(Long id) {
        agentConjointRepo.deleteById(id);
        return "Conjoint deleted";
    }
    @Override
    public List<AgentConjoint> getConjointAllList() {
        return agentConjointRepo.findAll();
    }

   
    
}