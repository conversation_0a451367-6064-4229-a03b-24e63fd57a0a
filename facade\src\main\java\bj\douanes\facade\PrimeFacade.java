package bj.douanes.facade;

import java.util.List;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Prime;
import bj.douanes.personal.service.PrimeServ;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface PrimeFacade {
    
    Prime getPrimeById(Long id);
    Prime createPrime(Long id,Object prime);
    String deletePrime(Long id);
    List<Prime> getPrimefAllList();
    Prime update(Long id,Object prime);
}

@Slf4j
@Service
@RequiredArgsConstructor
class PrimeFacadeImpl implements PrimeFacade{

    private final ModelMapper modelMapper;
    private final PrimeServ primeServ;
    
    @Override
    public Prime getPrimeById(Long id) {
        return primeServ.getPrimeById(id);
    }

    @Override
    public Prime createPrime(Long id, Object prime) {
       var newPrime = modelMapper.map(prime, Prime.class);
       return primeServ.createPrime(id, newPrime);
    }

    @Override
    public String deletePrime(Long id) {
        return primeServ.deletePrime(id);
    }

    @Override
    public List<Prime> getPrimefAllList() {
        return primeServ.getPrimefAllList();
    }

    @Override
    public Prime update(Long id, Object prime) {
        log.info("Implementation de la mise a jour");
        Prime prime2 = primeServ.getPrimeById(id);
        log.info("Avant le mapping: {}", prime2);
        modelMapper.map(prime, prime2);
        return primeServ.createPrime(id, prime2);
    }

    
}