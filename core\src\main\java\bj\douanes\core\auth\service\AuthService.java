package bj.douanes.core.auth.service;

import bj.douanes.core.auth.dto.AuthReq;
import bj.douanes.core.auth.model.User;
import bj.douanes.core.shared.error.ApiException;
import bj.douanes.core.shared.jwt.JwtUtils;
import org.modelmapper.ModelMapper;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import bj.douanes.core.auth.repository.UserRepo;
import lombok.RequiredArgsConstructor;

public interface AuthService {
    public AuthReq signUp(AuthReq registrationRequest);

    public AuthReq signIn(AuthReq signinRequest);
}

@Service
@RequiredArgsConstructor
class ServiceImpl implements AuthService {

    private final UserRepo repository;
    private final JwtUtils jwtUtils;
    private final AuthenticationManager authenticationManager;
    private final PasswordEncoder passwordEncoder;
    private final ModelMapper modelMapper;

    @Override
    public AuthReq signUp(AuthReq signupRequest) {
        try {
            User ourUsers = this.modelMapper.map(signupRequest, User.class);
            ourUsers.setPassword(passwordEncoder.encode(signupRequest.getPassword()));
            //
            ourUsers = repository.save(ourUsers);
            signupRequest.setId(ourUsers.getId());
            //
            var jwt = getJwt(signupRequest);
            signupRequest.setToken(jwt);
            // var refreshToken = jwtUtils.generateRefreshToken(new HashMap<>(), user);
            // signinRequest.setRefreshToken(refreshToken);
            signupRequest.setPassword(null);
            return signupRequest;
        } catch (BadCredentialsException e) {
            throw new ApiException(e);
        } catch (Exception e) {
            throw new ApiException("Email already exist.", e);
        }
    }

    @Override
    public AuthReq signIn(AuthReq signinRequest) {

        try {
            var jwt = getJwt(signinRequest);
            signinRequest.setToken(jwt);
            // var refreshToken = jwtUtils.generateRefreshToken(new HashMap<>(), user);
            // signinRequest.setRefreshToken(refreshToken);
            signinRequest.setPassword(null);
            return signinRequest;
        } catch (BadCredentialsException e) {
            throw new ApiException("Email or password not exist.");
        } catch (Exception e) {
            throw new ApiException(e);
        }
    }

    private String getJwt(AuthReq signinRequest) {
        Authentication auth = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(signinRequest.getEmail(), signinRequest.getPassword()));
        UserDetails user = (UserDetails) auth.getPrincipal();
        return jwtUtils.generateToken(user);
    }

}