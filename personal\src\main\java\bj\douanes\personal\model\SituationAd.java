package bj.douanes.personal.model;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonInclude;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Table(name = "SituationAdm")
public class SituationAd {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    private String matricule;
    private LocalDate datePriseServFp;
    private LocalDate datePriseServAdm;
    private Boolean nonActive;
    private String positionActuel;
    private String codeCollect;
    private String statusAgent;
    private String tel;
    private String structure;
    private String sousStructure;
    private String servBurAss;
    private String regimePension;
    private String profAvantEntrerService;
    private LocalDate dateNomEmploi;
    private String caisse;


    @OneToOne
    @JoinColumn(name = "agent_id")
    @JsonBackReference
    private Agent agent;
}
