package bj.douanes.core.config.database;

import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;

import org.springframework.core.env.Environment;

@Component
@RequiredArgsConstructor
public class DatabaseComponent {

    private final Environment env;

    public String getEnv(String sourceType, String sourceName) {
        return this.env.getProperty(String.format("app.datasource.%s.%s", sourceType, sourceName));
    }
}
