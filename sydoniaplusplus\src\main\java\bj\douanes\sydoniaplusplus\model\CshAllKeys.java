package bj.douanes.sydoniaplusplus.model;

import java.io.Serializable;

import org.springframework.data.relational.core.mapping.Column;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class CshAllKeys implements Serializable {
    
    @Column("RCP_YEAR")
    private String rcpYear;

    @Column("RCP_CUO")
    private String rcpCuo;

    @Column("RCP_SERIAL")
    private String rcpSerial;

    @Column("RCP_NBER")
    private String rcpNber;
}
