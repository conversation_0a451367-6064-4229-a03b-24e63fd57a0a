/*! For license information please see 196.67f66071.chunk.js.LICENSE.txt */
(self.webpackChunkfrontend_erp_douanes_benin=self.webpackChunkfrontend_erp_douanes_benin||[]).push([[196],{9157:(e,t,n)=>{"use strict";n.d(t,{A:()=>Pe});var o=n(5043),r=n(6191),a=n(8139),i=n.n(a),c=n(8168),l=n(9379),s=n(4467),u=n(5544),d=n(2284),p=n(45),f=n(8419),m=n(8678),g=["crossOrigin","decoding","draggable","loading","referrerPolicy","sizes","srcSet","useMap","alt"],v=o.createContext(null),b=0;function h(e){var t=e.src,n=e.isCustomPlaceholder,r=e.fallback,a=(0,o.useState)(n?"loading":"normal"),i=(0,u.A)(a,2),c=i[0],l=i[1],s=(0,o.useRef)(!1),d="error"===c;(0,o.useEffect)((function(){var e=!0;return function(e){return new Promise((function(t){var n=document.createElement("img");n.onerror=function(){return t(!1)},n.onload=function(){return t(!0)},n.src=e}))}(t).then((function(t){!t&&e&&l("error")})),function(){e=!1}}),[t]),(0,o.useEffect)((function(){n&&!s.current?l("loading"):d&&l("normal")}),[t]);var p=function(){l("normal")};return[function(e){s.current=!1,"loading"===c&&null!==e&&void 0!==e&&e.complete&&(e.naturalWidth||e.naturalHeight)&&(s.current=!0,p())},d&&r?{src:r}:{onLoad:p,src:t},c]}var y=n(9743),w=n(4903),x=n(5001),C=n(2231),S=n(5818),A={x:0,y:0,rotate:0,scale:1,flipX:!1,flipY:!1};var E=n(7907);function O(e,t,n,o){var r=t+n,a=(n-o)/2;if(n>o){if(t>0)return(0,s.A)({},e,a);if(t<0&&r<o)return(0,s.A)({},e,-a)}else if(t<0||r>o)return(0,s.A)({},e,t<0?a:-a);return{}}function k(e,t,n,o){var r=(0,f.XV)(),a=r.width,i=r.height,c=null;return e<=a&&t<=i?c={x:0,y:0}:(e>a||t>i)&&(c=(0,l.A)((0,l.A)({},O("x",n,e,a)),O("y",o,t,i))),c}function j(e,t){var n=e.x-t.x,o=e.y-t.y;return Math.hypot(n,o)}function I(e,t,n,r,a,i,c){var s=a.rotate,d=a.scale,p=a.x,f=a.y,m=(0,o.useState)(!1),g=(0,u.A)(m,2),v=g[0],b=g[1],h=(0,o.useRef)({point1:{x:0,y:0},point2:{x:0,y:0},eventType:"none"}),y=function(e){h.current=(0,l.A)((0,l.A)({},h.current),e)};return(0,o.useEffect)((function(){var e;return n&&t&&(e=(0,w.A)(window,"touchmove",(function(e){return e.preventDefault()}),{passive:!1})),function(){var t;null===(t=e)||void 0===t||t.remove()}}),[n,t]),{isTouching:v,onTouchStart:function(e){if(t){e.stopPropagation(),b(!0);var n=e.touches,o=void 0===n?[]:n;o.length>1?y({point1:{x:o[0].clientX,y:o[0].clientY},point2:{x:o[1].clientX,y:o[1].clientY},eventType:"touchZoom"}):y({point1:{x:o[0].clientX-p,y:o[0].clientY-f},eventType:"move"})}},onTouchMove:function(e){var t=e.touches,n=void 0===t?[]:t,o=h.current,r=o.point1,a=o.point2,l=o.eventType;if(n.length>1&&"touchZoom"===l){var s={x:n[0].clientX,y:n[0].clientY},d={x:n[1].clientX,y:n[1].clientY},p=function(e,t,n,o){var r=j(e,n),a=j(t,o);if(0===r&&0===a)return[e.x,e.y];var i=r/(r+a);return[e.x+i*(t.x-e.x),e.y+i*(t.y-e.y)]}(r,a,s,d),f=(0,u.A)(p,2),m=f[0],g=f[1],v=j(s,d)/j(r,a);c(v,"touchZoom",m,g,!0),y({point1:s,point2:d,eventType:"touchZoom"})}else"move"===l&&(i({x:n[0].clientX-r.x,y:n[0].clientY-r.y},"move"),y({eventType:"move"}))},onTouchEnd:function(){if(n){if(v&&b(!1),y({eventType:"none"}),r>d)return i({x:0,y:0,scale:r},"touchZoom");var t=e.current.offsetWidth*d,o=e.current.offsetHeight*d,a=e.current.getBoundingClientRect(),c=a.left,u=a.top,p=s%180!==0,f=k(p?o:t,p?t:o,c,u);f&&i((0,l.A)({},f),"dragRebound")}}}}var R=n(4347),M=n(7419);const N=function(e){var t=e.visible,n=e.maskTransitionName,r=e.getContainer,a=e.prefixCls,c=e.rootClassName,u=e.icons,d=e.countRender,p=e.showSwitch,f=e.showProgress,m=e.current,g=e.transform,b=e.count,h=e.scale,y=e.minScale,w=e.maxScale,C=e.closeIcon,S=e.onSwitchLeft,A=e.onSwitchRight,E=e.onClose,O=e.onZoomIn,k=e.onZoomOut,j=e.onRotateRight,I=e.onRotateLeft,N=e.onFlipX,z=e.onFlipY,T=e.toolbarRender,P=e.zIndex,D=(0,o.useContext)(v),L=u.rotateLeft,H=u.rotateRight,Y=u.zoomIn,X=u.zoomOut,B=u.close,W=u.left,F=u.right,V=u.flipX,Z=u.flipY,q="".concat(a,"-operations-operation");o.useEffect((function(){var e=function(e){e.keyCode===x.A.ESC&&E()};return t&&window.addEventListener("keydown",e),function(){window.removeEventListener("keydown",e)}}),[t]);var U=[{icon:Z,onClick:z,type:"flipY"},{icon:V,onClick:N,type:"flipX"},{icon:L,onClick:I,type:"rotateLeft"},{icon:H,onClick:j,type:"rotateRight"},{icon:X,onClick:k,type:"zoomOut",disabled:h<=y},{icon:Y,onClick:O,type:"zoomIn",disabled:h===w}].map((function(e){var t,n=e.icon,r=e.onClick,c=e.type,l=e.disabled;return o.createElement("div",{className:i()(q,(t={},(0,s.A)(t,"".concat(a,"-operations-operation-").concat(c),!0),(0,s.A)(t,"".concat(a,"-operations-operation-disabled"),!!l),t)),onClick:r,key:c},n)})),_=o.createElement("div",{className:"".concat(a,"-operations")},U);return o.createElement(M.Ay,{visible:t,motionName:n},(function(e){var t=e.className,n=e.style;return o.createElement(R.A,{open:!0,getContainer:null!==r&&void 0!==r?r:document.body},o.createElement("div",{className:i()("".concat(a,"-operations-wrapper"),t,c),style:(0,l.A)((0,l.A)({},n),{},{zIndex:P})},null===C?null:o.createElement("button",{className:"".concat(a,"-close"),onClick:E},C||B),p&&o.createElement(o.Fragment,null,o.createElement("div",{className:i()("".concat(a,"-switch-left"),(0,s.A)({},"".concat(a,"-switch-left-disabled"),0===m)),onClick:S},W),o.createElement("div",{className:i()("".concat(a,"-switch-right"),(0,s.A)({},"".concat(a,"-switch-right-disabled"),m===b-1)),onClick:A},F)),o.createElement("div",{className:"".concat(a,"-footer")},f&&o.createElement("div",{className:"".concat(a,"-progress")},d?d(m+1,b):"".concat(m+1," / ").concat(b)),T?T(_,(0,l.A)({icons:{flipYIcon:U[0],flipXIcon:U[1],rotateLeftIcon:U[2],rotateRightIcon:U[3],zoomOutIcon:U[4],zoomInIcon:U[5]},actions:{onFlipY:z,onFlipX:N,onRotateLeft:I,onRotateRight:j,onZoomOut:k,onZoomIn:O},transform:g},D?{current:m,total:b}:{})):_)))}))};var z=["fallback","src","imgRef"],T=["prefixCls","src","alt","fallback","movable","onClose","visible","icons","rootClassName","closeIcon","getContainer","current","count","countRender","scaleStep","minScale","maxScale","transitionName","maskTransitionName","imageRender","imgCommonProps","toolbarRender","onTransform","onChange"],P=function(e){var t=e.fallback,n=e.src,r=e.imgRef,a=(0,p.A)(e,z),i=h({src:n,fallback:t}),l=(0,u.A)(i,2),s=l[0],d=l[1];return o.createElement("img",(0,c.A)({ref:function(e){r.current=e,s(e)}},a,d))};const D=function(e){var t=e.prefixCls,n=e.src,r=e.alt,a=e.fallback,d=e.movable,m=void 0===d||d,g=e.onClose,b=e.visible,h=e.icons,O=void 0===h?{}:h,j=e.rootClassName,R=e.closeIcon,M=e.getContainer,z=e.current,D=void 0===z?0:z,L=e.count,H=void 0===L?1:L,Y=e.countRender,X=e.scaleStep,B=void 0===X?.5:X,W=e.minScale,F=void 0===W?1:W,V=e.maxScale,Z=void 0===V?50:V,q=e.transitionName,U=void 0===q?"zoom":q,_=e.maskTransitionName,K=void 0===_?"fade":_,G=e.imageRender,Q=e.imgCommonProps,J=e.toolbarRender,$=e.onTransform,ee=e.onChange,te=(0,p.A)(e,T),ne=(0,o.useRef)(),oe=(0,o.useContext)(v),re=oe&&H>1,ae=oe&&H>=1,ie=(0,o.useState)(!0),ce=(0,u.A)(ie,2),le=ce[0],se=ce[1],ue=function(e,t,n,r){var a=(0,o.useRef)(null),i=(0,o.useRef)([]),c=(0,o.useState)(A),s=(0,u.A)(c,2),d=s[0],p=s[1],m=function(e,t){null===a.current&&(i.current=[],a.current=(0,S.A)((function(){p((function(e){var n=e;return i.current.forEach((function(e){n=(0,l.A)((0,l.A)({},n),e)})),a.current=null,null===r||void 0===r||r({transform:n,action:t}),n}))}))),i.current.push((0,l.A)((0,l.A)({},d),e))};return{transform:d,resetTransform:function(e){p(A),r&&!(0,C.A)(A,d)&&r({transform:A,action:e})},updateTransform:m,dispatchZoomChange:function(o,r,a,i,c){var l=e.current,s=l.width,u=l.height,p=l.offsetWidth,g=l.offsetHeight,v=l.offsetLeft,b=l.offsetTop,h=o,y=d.scale*o;y>n?(y=n,h=n/d.scale):y<t&&(h=(y=c?y:t)/d.scale);var w=null!==a&&void 0!==a?a:innerWidth/2,x=null!==i&&void 0!==i?i:innerHeight/2,C=h-1,S=C*s*.5,A=C*u*.5,E=C*(w-d.x-v),O=C*(x-d.y-b),k=d.x-(E-S),j=d.y-(O-A);if(o<1&&1===y){var I=p*y,R=g*y,M=(0,f.XV)(),N=M.width,z=M.height;I<=N&&R<=z&&(k=0,j=0)}m({x:k,y:j,scale:y},r)}}}(ne,F,Z,$),de=ue.transform,pe=ue.resetTransform,fe=ue.updateTransform,me=ue.dispatchZoomChange,ge=function(e,t,n,r,a,i,c){var s=a.rotate,d=a.scale,p=a.x,f=a.y,m=(0,o.useState)(!1),g=(0,u.A)(m,2),v=g[0],b=g[1],h=(0,o.useRef)({diffX:0,diffY:0,transformX:0,transformY:0}),y=function(e){n&&v&&i({x:e.pageX-h.current.diffX,y:e.pageY-h.current.diffY},"move")},x=function(){if(n&&v){b(!1);var t=h.current,o=t.transformX,r=t.transformY;if(p===o||f===r)return;var a=e.current.offsetWidth*d,c=e.current.offsetHeight*d,u=e.current.getBoundingClientRect(),m=u.left,g=u.top,y=s%180!==0,w=k(y?c:a,y?a:c,m,g);w&&i((0,l.A)({},w),"dragRebound")}};return(0,o.useEffect)((function(){var e,n,o,r;if(t){o=(0,w.A)(window,"mouseup",x,!1),r=(0,w.A)(window,"mousemove",y,!1);try{window.top!==window.self&&(e=(0,w.A)(window.top,"mouseup",x,!1),n=(0,w.A)(window.top,"mousemove",y,!1))}catch(a){(0,E.$e)(!1,"[rc-image] ".concat(a))}}return function(){var t,a,i,c;null===(t=o)||void 0===t||t.remove(),null===(a=r)||void 0===a||a.remove(),null===(i=e)||void 0===i||i.remove(),null===(c=n)||void 0===c||c.remove()}}),[n,v,p,f,s,t]),{isMoving:v,onMouseDown:function(e){t&&0===e.button&&(e.preventDefault(),e.stopPropagation(),h.current={diffX:e.pageX-p,diffY:e.pageY-f,transformX:p,transformY:f},b(!0))},onMouseMove:y,onMouseUp:x,onWheel:function(e){if(n&&0!=e.deltaY){var t=Math.abs(e.deltaY/100),o=1+Math.min(t,1)*r;e.deltaY>0&&(o=1/o),c(o,"wheel",e.clientX,e.clientY)}}}}(ne,m,b,B,de,fe,me),ve=ge.isMoving,be=ge.onMouseDown,he=ge.onWheel,ye=I(ne,m,b,F,de,fe,me),we=ye.isTouching,xe=ye.onTouchStart,Ce=ye.onTouchMove,Se=ye.onTouchEnd,Ae=de.rotate,Ee=de.scale,Oe=i()((0,s.A)({},"".concat(t,"-moving"),ve));(0,o.useEffect)((function(){le||se(!0)}),[le]);var ke=function(e){null===e||void 0===e||e.preventDefault(),null===e||void 0===e||e.stopPropagation(),D>0&&(se(!1),pe("prev"),null===ee||void 0===ee||ee(D-1,D))},je=function(e){null===e||void 0===e||e.preventDefault(),null===e||void 0===e||e.stopPropagation(),D<H-1&&(se(!1),pe("next"),null===ee||void 0===ee||ee(D+1,D))},Ie=function(e){b&&re&&(e.keyCode===x.A.LEFT?ke():e.keyCode===x.A.RIGHT&&je())};(0,o.useEffect)((function(){var e=(0,w.A)(window,"keydown",Ie,!1);return function(){e.remove()}}),[b,re,D]);var Re=o.createElement(P,(0,c.A)({},Q,{width:e.width,height:e.height,imgRef:ne,className:"".concat(t,"-img"),alt:r,style:{transform:"translate3d(".concat(de.x,"px, ").concat(de.y,"px, 0) scale3d(").concat(de.flipX?"-":"").concat(Ee,", ").concat(de.flipY?"-":"").concat(Ee,", 1) rotate(").concat(Ae,"deg)"),transitionDuration:(!le||we)&&"0s"},fallback:a,src:n,onWheel:he,onMouseDown:be,onDoubleClick:function(e){b&&(1!==Ee?fe({x:0,y:0,scale:1},"doubleClick"):me(1+B,"doubleClick",e.clientX,e.clientY))},onTouchStart:xe,onTouchMove:Ce,onTouchEnd:Se,onTouchCancel:Se}));return o.createElement(o.Fragment,null,o.createElement(y.A,(0,c.A)({transitionName:U,maskTransitionName:K,closable:!1,keyboard:!0,prefixCls:t,onClose:g,visible:b,classNames:{wrapper:Oe},rootClassName:j,getContainer:M},te,{afterClose:function(){pe("close")}}),o.createElement("div",{className:"".concat(t,"-img-wrapper")},G?G(Re,(0,l.A)({transform:de},oe?{current:D}:{})):Re)),o.createElement(N,{visible:b,transform:de,maskTransitionName:K,closeIcon:R,getContainer:M,prefixCls:t,rootClassName:j,icons:O,countRender:Y,showSwitch:re,showProgress:ae,current:D,count:H,scale:Ee,minScale:F,maxScale:Z,toolbarRender:J,onSwitchLeft:ke,onSwitchRight:je,onZoomIn:function(){me(1+B,"zoomIn")},onZoomOut:function(){me(1/(1+B),"zoomOut")},onRotateRight:function(){fe({rotate:Ae+90},"rotateRight")},onRotateLeft:function(){fe({rotate:Ae-90},"rotateLeft")},onFlipX:function(){fe({flipX:!de.flipX},"flipX")},onFlipY:function(){fe({flipY:!de.flipY},"flipY")},onClose:g,zIndex:void 0!==te.zIndex?te.zIndex+1:void 0}))};var L=n(436);var H=["visible","onVisibleChange","getContainer","current","movable","minScale","maxScale","countRender","closeIcon","onChange","onTransform","toolbarRender","imageRender"],Y=["src"];const X=function(e){var t,n=e.previewPrefixCls,r=void 0===n?"rc-image-preview":n,a=e.children,i=e.icons,f=void 0===i?{}:i,b=e.items,h=e.preview,y=e.fallback,w="object"===(0,d.A)(h)?h:{},x=w.visible,C=w.onVisibleChange,S=w.getContainer,A=w.current,E=w.movable,O=w.minScale,k=w.maxScale,j=w.countRender,I=w.closeIcon,R=w.onChange,M=w.onTransform,N=w.toolbarRender,z=w.imageRender,T=(0,p.A)(w,H),P=function(e){var t=o.useState({}),n=(0,u.A)(t,2),r=n[0],a=n[1],i=o.useCallback((function(e,t){return a((function(n){return(0,l.A)((0,l.A)({},n),{},(0,s.A)({},e,t))})),function(){a((function(t){var n=(0,l.A)({},t);return delete n[e],n}))}}),[]);return[o.useMemo((function(){return e?e.map((function(e){if("string"===typeof e)return{data:{src:e}};var t={};return Object.keys(e).forEach((function(n){["src"].concat((0,L.A)(g)).includes(n)&&(t[n]=e[n])})),{data:t}})):Object.keys(r).reduce((function(e,t){var n=r[t],o=n.canPreview,a=n.data;return o&&e.push({data:a,id:t}),e}),[])}),[e,r]),i]}(b),X=(0,u.A)(P,2),B=X[0],W=X[1],F=(0,m.A)(0,{value:A}),V=(0,u.A)(F,2),Z=V[0],q=V[1],U=(0,o.useState)(!1),_=(0,u.A)(U,2),K=_[0],G=_[1],Q=(null===(t=B[Z])||void 0===t?void 0:t.data)||{},J=Q.src,$=(0,p.A)(Q,Y),ee=(0,m.A)(!!x,{value:x,onChange:function(e,t){null===C||void 0===C||C(e,t,Z)}}),te=(0,u.A)(ee,2),ne=te[0],oe=te[1],re=(0,o.useState)(null),ae=(0,u.A)(re,2),ie=ae[0],ce=ae[1],le=o.useCallback((function(e,t,n){var o=B.findIndex((function(t){return t.id===e}));oe(!0),ce({x:t,y:n}),q(o<0?0:o),G(!0)}),[B]);o.useEffect((function(){ne?K||q(0):G(!1)}),[ne]);var se=o.useMemo((function(){return{register:W,onPreview:le}}),[W,le]);return o.createElement(v.Provider,{value:se},a,o.createElement(D,(0,c.A)({"aria-hidden":!ne,movable:E,visible:ne,prefixCls:r,closeIcon:I,onClose:function(){oe(!1),ce(null)},mousePosition:ie,imgCommonProps:$,src:J,fallback:y,icons:f,minScale:O,maxScale:k,getContainer:S,current:Z,count:B.length,countRender:j,onTransform:M,toolbarRender:N,imageRender:z,onChange:function(e,t){q(e),null===R||void 0===R||R(e,t)}},T)))};var B=["src","alt","onPreviewClose","prefixCls","previewPrefixCls","placeholder","fallback","width","height","style","preview","className","onClick","onError","wrapperClassName","wrapperStyle","rootClassName"],W=["src","visible","onVisibleChange","getContainer","mask","maskClassName","movable","icons","scaleStep","minScale","maxScale","imageRender","toolbarRender"],F=function(e){var t=e.src,n=e.alt,r=e.onPreviewClose,a=e.prefixCls,y=void 0===a?"rc-image":a,w=e.previewPrefixCls,x=void 0===w?"".concat(y,"-preview"):w,C=e.placeholder,S=e.fallback,A=e.width,E=e.height,O=e.style,k=e.preview,j=void 0===k||k,I=e.className,R=e.onClick,M=e.onError,N=e.wrapperClassName,z=e.wrapperStyle,T=e.rootClassName,P=(0,p.A)(e,B),L=C&&!0!==C,H="object"===(0,d.A)(j)?j:{},Y=H.src,X=H.visible,F=void 0===X?void 0:X,V=H.onVisibleChange,Z=void 0===V?r:V,q=H.getContainer,U=void 0===q?void 0:q,_=H.mask,K=H.maskClassName,G=H.movable,Q=H.icons,J=H.scaleStep,$=H.minScale,ee=H.maxScale,te=H.imageRender,ne=H.toolbarRender,oe=(0,p.A)(H,W),re=null!==Y&&void 0!==Y?Y:t,ae=(0,m.A)(!!F,{value:F,onChange:Z}),ie=(0,u.A)(ae,2),ce=ie[0],le=ie[1],se=h({src:t,isCustomPlaceholder:L,fallback:S}),ue=(0,u.A)(se,3),de=ue[0],pe=ue[1],fe=ue[2],me=(0,o.useState)(null),ge=(0,u.A)(me,2),ve=ge[0],be=ge[1],he=(0,o.useContext)(v),ye=!!j,we=i()(y,N,T,(0,s.A)({},"".concat(y,"-error"),"error"===fe)),xe=(0,o.useMemo)((function(){var t={};return g.forEach((function(n){void 0!==e[n]&&(t[n]=e[n])})),t}),g.map((function(t){return e[t]}))),Ce=function(e,t){var n=o.useState((function(){return String(b+=1)})),r=(0,u.A)(n,1)[0],a=o.useContext(v),i={data:t,canPreview:e};return o.useEffect((function(){if(a)return a.register(r,i)}),[]),o.useEffect((function(){a&&a.register(r,i)}),[e,t]),r}(ye,(0,o.useMemo)((function(){return(0,l.A)((0,l.A)({},xe),{},{src:re})}),[re,xe]));return o.createElement(o.Fragment,null,o.createElement("div",(0,c.A)({},P,{className:we,onClick:ye?function(e){var t=(0,f.A3)(e.target),n=t.left,o=t.top;he?he.onPreview(Ce,n,o):(be({x:n,y:o}),le(!0)),null===R||void 0===R||R(e)}:R,style:(0,l.A)({width:A,height:E},z)}),o.createElement("img",(0,c.A)({},xe,{className:i()("".concat(y,"-img"),(0,s.A)({},"".concat(y,"-img-placeholder"),!0===C),I),style:(0,l.A)({height:E},O),ref:de},pe,{width:A,height:E,onError:M})),"loading"===fe&&o.createElement("div",{"aria-hidden":"true",className:"".concat(y,"-placeholder")},C),_&&ye&&o.createElement("div",{className:i()("".concat(y,"-mask"),K),style:{display:"none"===(null===O||void 0===O?void 0:O.display)?"none":void 0}},_)),!he&&ye&&o.createElement(D,(0,c.A)({"aria-hidden":!ce,visible:ce,prefixCls:x,onClose:function(){le(!1),be(null)},mousePosition:ve,src:re,alt:n,fallback:S,getContainer:U,icons:Q,movable:G,scaleStep:J,minScale:$,maxScale:ee,rootClassName:T,imageRender:te,imgCommonProps:xe,toolbarRender:ne},oe)))};F.PreviewGroup=X,F.displayName="Image";const V=F;var Z=n(4980),q=n(3290),U=n(5296),_=n(8887),K=n(4440),G=n(3727),Q=n(682),J=n(9905);const $={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M672 418H144c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H188V494h440v326z"}},{tag:"path",attrs:{d:"M819.3 328.5c-78.8-100.7-196-153.6-314.6-154.2l-.2-64c0-6.5-7.6-10.1-12.6-6.1l-128 101c-4 3.1-3.9 9.1 0 12.3L492 318.6c5.1 4 12.7.4 12.6-6.1v-63.9c12.9.1 25.9.9 38.8 2.5 42.1 5.2 82.1 18.2 119 38.7 38.1 21.2 71.2 49.7 98.4 84.3 27.1 34.7 46.7 73.7 58.1 115.8a325.95 325.95 0 016.5 140.9h74.9c14.8-103.6-11.3-213-81-302.3z"}}]},name:"rotate-left",theme:"outlined"};var ee=n(2172),te=function(e,t){return o.createElement(ee.A,(0,c.A)({},e,{ref:t,icon:$}))};const ne=o.forwardRef(te);const oe={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M480.5 251.2c13-1.6 25.9-2.4 38.8-2.5v63.9c0 6.5 7.5 10.1 12.6 6.1L660 217.6c4-3.2 4-9.2 0-12.3l-128-101c-5.1-4-12.6-.4-12.6 6.1l-.2 64c-118.6.5-235.8 53.4-314.6 154.2A399.75 399.75 0 00123.5 631h74.9c-.9-5.3-1.7-10.7-2.4-16.1-5.1-42.1-2.1-84.1 8.9-124.8 11.4-42.2 31-81.1 58.1-115.8 27.2-34.7 60.3-63.2 98.4-84.3 37-20.6 76.9-33.6 119.1-38.8z"}},{tag:"path",attrs:{d:"M880 418H352c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H396V494h440v326z"}}]},name:"rotate-right",theme:"outlined"};var re=function(e,t){return o.createElement(ee.A,(0,c.A)({},e,{ref:t,icon:oe}))};const ae=o.forwardRef(re);const ie={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"};var ce=function(e,t){return o.createElement(ee.A,(0,c.A)({},e,{ref:t,icon:ie}))};const le=o.forwardRef(ce);const se={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-in",theme:"outlined"};var ue=function(e,t){return o.createElement(ee.A,(0,c.A)({},e,{ref:t,icon:se}))};const de=o.forwardRef(ue);const pe={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h312c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-out",theme:"outlined"};var fe=function(e,t){return o.createElement(ee.A,(0,c.A)({},e,{ref:t,icon:pe}))};const me=o.forwardRef(fe);var ge=n(9310),ve=n(97),be=n(4152),he=n(4414),ye=n(5814),we=n(3183),xe=n(7060),Ce=n(8365);const Se=e=>({position:e||"absolute",inset:0}),Ae=e=>{const{iconCls:t,motionDurationSlow:n,paddingXXS:o,marginXXS:r,prefixCls:a,colorTextLightSolid:i}=e;return{position:"absolute",inset:0,display:"flex",alignItems:"center",justifyContent:"center",color:i,background:new ve.q("#000").setAlpha(.5).toRgbString(),cursor:"pointer",opacity:0,transition:"opacity ".concat(n),[".".concat(a,"-mask-info")]:Object.assign(Object.assign({},he.L9),{padding:"0 ".concat((0,ge.zA)(o)),[t]:{marginInlineEnd:r,svg:{verticalAlign:"baseline"}}})}},Ee=e=>{const{previewCls:t,modalMaskBg:n,paddingSM:o,marginXL:r,margin:a,paddingLG:i,previewOperationColorDisabled:c,previewOperationHoverColor:l,motionDurationSlow:s,iconCls:u,colorTextLightSolid:d}=e,p=new ve.q(n).setAlpha(.1),f=p.clone().setAlpha(.2);return{["".concat(t,"-footer")]:{position:"fixed",bottom:r,left:{_skip_check_:!0,value:0},width:"100%",display:"flex",flexDirection:"column",alignItems:"center",color:e.previewOperationColor},["".concat(t,"-progress")]:{marginBottom:a},["".concat(t,"-close")]:{position:"fixed",top:r,right:{_skip_check_:!0,value:r},display:"flex",color:d,backgroundColor:p.toRgbString(),borderRadius:"50%",padding:o,outline:0,border:0,cursor:"pointer",transition:"all ".concat(s),"&:hover":{backgroundColor:f.toRgbString()},["& > ".concat(u)]:{fontSize:e.previewOperationSize}},["".concat(t,"-operations")]:{display:"flex",alignItems:"center",padding:"0 ".concat((0,ge.zA)(i)),backgroundColor:p.toRgbString(),borderRadius:100,"&-operation":{marginInlineStart:o,padding:o,cursor:"pointer",transition:"all ".concat(s),userSelect:"none",["&:not(".concat(t,"-operations-operation-disabled):hover > ").concat(u)]:{color:l},"&-disabled":{color:c,cursor:"not-allowed"},"&:first-of-type":{marginInlineStart:0},["& > ".concat(u)]:{fontSize:e.previewOperationSize}}}}},Oe=e=>{const{modalMaskBg:t,iconCls:n,previewOperationColorDisabled:o,previewCls:r,zIndexPopup:a,motionDurationSlow:i}=e,c=new ve.q(t).setAlpha(.1),l=c.clone().setAlpha(.2);return{["".concat(r,"-switch-left, ").concat(r,"-switch-right")]:{position:"fixed",insetBlockStart:"50%",zIndex:e.calc(a).add(1).equal({unit:!1}),display:"flex",alignItems:"center",justifyContent:"center",width:e.imagePreviewSwitchSize,height:e.imagePreviewSwitchSize,marginTop:e.calc(e.imagePreviewSwitchSize).mul(-1).div(2).equal(),color:e.previewOperationColor,background:c.toRgbString(),borderRadius:"50%",transform:"translateY(-50%)",cursor:"pointer",transition:"all ".concat(i),userSelect:"none","&:hover":{background:l.toRgbString()},"&-disabled":{"&, &:hover":{color:o,background:"transparent",cursor:"not-allowed",["> ".concat(n)]:{cursor:"not-allowed"}}},["> ".concat(n)]:{fontSize:e.previewOperationSize}},["".concat(r,"-switch-left")]:{insetInlineStart:e.marginSM},["".concat(r,"-switch-right")]:{insetInlineEnd:e.marginSM}}},ke=e=>{const{motionEaseOut:t,previewCls:n,motionDurationSlow:o,componentCls:r}=e;return[{["".concat(r,"-preview-root")]:{[n]:{height:"100%",textAlign:"center",pointerEvents:"none"},["".concat(n,"-body")]:Object.assign(Object.assign({},Se()),{overflow:"hidden"}),["".concat(n,"-img")]:{maxWidth:"100%",maxHeight:"70%",verticalAlign:"middle",transform:"scale3d(1, 1, 1)",cursor:"grab",transition:"transform ".concat(o," ").concat(t," 0s"),userSelect:"none","&-wrapper":Object.assign(Object.assign({},Se()),{transition:"transform ".concat(o," ").concat(t," 0s"),display:"flex",justifyContent:"center",alignItems:"center","& > *":{pointerEvents:"auto"},"&::before":{display:"inline-block",width:1,height:"50%",marginInlineEnd:-1,content:'""'}})},["".concat(n,"-moving")]:{["".concat(n,"-preview-img")]:{cursor:"grabbing","&-wrapper":{transitionDuration:"0s"}}}}},{["".concat(r,"-preview-root")]:{["".concat(n,"-wrap")]:{zIndex:e.zIndexPopup}}},{["".concat(r,"-preview-operations-wrapper")]:{position:"fixed",zIndex:e.calc(e.zIndexPopup).add(1).equal({unit:!1})},"&":[Ee(e),Oe(e)]}]},je=e=>{const{componentCls:t}=e;return{[t]:{position:"relative",display:"inline-block",["".concat(t,"-img")]:{width:"100%",height:"auto",verticalAlign:"middle"},["".concat(t,"-img-placeholder")]:{backgroundColor:e.colorBgContainerDisabled,backgroundImage:"url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTQuNSAyLjVoLTEzQS41LjUgMCAwIDAgMSAzdjEwYS41LjUgMCAwIDAgLjUuNWgxM2EuNS41IDAgMCAwIC41LS41VjNhLjUuNSAwIDAgMC0uNS0uNXpNNS4yODEgNC43NWExIDEgMCAwIDEgMCAyIDEgMSAwIDAgMSAwLTJ6bTguMDMgNi44M2EuMTI3LjEyNyAwIDAgMS0uMDgxLjAzSDIuNzY5YS4xMjUuMTI1IDAgMCAxLS4wOTYtLjIwN2wyLjY2MS0zLjE1NmEuMTI2LjEyNiAwIDAgMSAuMTc3LS4wMTZsLjAxNi4wMTZMNy4wOCAxMC4wOWwyLjQ3LTIuOTNhLjEyNi4xMjYgMCAwIDEgLjE3Ny0uMDE2bC4wMTUuMDE2IDMuNTg4IDQuMjQ0YS4xMjcuMTI3IDAgMCAxLS4wMi4xNzV6IiBmaWxsPSIjOEM4QzhDIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=')",backgroundRepeat:"no-repeat",backgroundPosition:"center center",backgroundSize:"30%"},["".concat(t,"-mask")]:Object.assign({},Ae(e)),["".concat(t,"-mask:hover")]:{opacity:1},["".concat(t,"-placeholder")]:Object.assign({},Se())}}},Ie=e=>{const{previewCls:t}=e;return{["".concat(t,"-root")]:(0,ye.aB)(e,"zoom"),"&":(0,we.p9)(e,!0)}},Re=(0,xe.OF)("Image",(e=>{const t="".concat(e.componentCls,"-preview"),n=(0,Ce.h1)(e,{previewCls:t,modalMaskBg:new ve.q("#000").setAlpha(.45).toRgbString(),imagePreviewSwitchSize:e.controlHeightLG});return[je(n),ke(n),(0,be.Dk)((0,Ce.h1)(n,{componentCls:t})),Ie(n)]}),(e=>({zIndexPopup:e.zIndexPopupBase+80,previewOperationColor:new ve.q(e.colorTextLightSolid).setAlpha(.65).toRgbString(),previewOperationHoverColor:new ve.q(e.colorTextLightSolid).setAlpha(.85).toRgbString(),previewOperationColorDisabled:new ve.q(e.colorTextLightSolid).setAlpha(.25).toRgbString(),previewOperationSize:1.5*e.fontSizeIcon})));var Me=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const Ne={rotateLeft:o.createElement(ne,null),rotateRight:o.createElement(ae,null),zoomIn:o.createElement(de,null),zoomOut:o.createElement(me,null),close:o.createElement(G.A,null),left:o.createElement(Q.A,null),right:o.createElement(J.A,null),flipX:o.createElement(le,null),flipY:o.createElement(le,{rotate:90})};var ze=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const Te=e=>{var t;const{prefixCls:n,preview:a,className:c,rootClassName:l,style:s}=e,u=ze(e,["prefixCls","preview","className","rootClassName","style"]),{getPrefixCls:d,locale:p=K.A,getPopupContainer:f,image:m}=o.useContext(U.QO),g=d("image",n),v=d(),b=p.Image||K.A.Image,h=(0,_.A)(g),[y,w,x]=Re(g,h),C=i()(l,w,x,h),S=i()(c,w,null===m||void 0===m?void 0:m.className),[A]=(0,Z.YK)("ImagePreview","object"===typeof a?a.zIndex:void 0),E=o.useMemo((()=>{var e;if(!1===a)return a;const t="object"===typeof a?a:{},{getContainer:n,closeIcon:i}=t,c=ze(t,["getContainer","closeIcon"]);return Object.assign(Object.assign({mask:o.createElement("div",{className:"".concat(g,"-mask-info")},o.createElement(r.A,null),null===b||void 0===b?void 0:b.preview),icons:Ne},c),{getContainer:null!==n&&void 0!==n?n:f,transitionName:(0,q.b)(v,"zoom",t.transitionName),maskTransitionName:(0,q.b)(v,"fade",t.maskTransitionName),zIndex:A,closeIcon:null!==i&&void 0!==i?i:null===(e=null===m||void 0===m?void 0:m.preview)||void 0===e?void 0:e.closeIcon})}),[a,b,null===(t=null===m||void 0===m?void 0:m.preview)||void 0===t?void 0:t.closeIcon]),O=Object.assign(Object.assign({},null===m||void 0===m?void 0:m.style),s);return y(o.createElement(V,Object.assign({prefixCls:g,preview:E,rootClassName:C,className:S,style:O},u)))};Te.PreviewGroup=e=>{var{previewPrefixCls:t,preview:n}=e,r=Me(e,["previewPrefixCls","preview"]);const{getPrefixCls:a}=o.useContext(U.QO),c=a("image",t),l="".concat(c,"-preview"),s=a(),u=(0,_.A)(c),[d,p,f]=Re(c,u),[m]=(0,Z.YK)("ImagePreview","object"===typeof n?n.zIndex:void 0),g=o.useMemo((()=>{var e;if(!1===n)return n;const t="object"===typeof n?n:{},o=i()(p,f,u,null!==(e=t.rootClassName)&&void 0!==e?e:"");return Object.assign(Object.assign({},t),{transitionName:(0,q.b)(s,"zoom",t.transitionName),maskTransitionName:(0,q.b)(s,"fade",t.maskTransitionName),rootClassName:o,zIndex:m})}),[n]);return d(o.createElement(V.PreviewGroup,Object.assign({preview:g,previewPrefixCls:l,icons:Ne},r)))};const Pe=Te},5800:(e,t,n)=>{"use strict";n.d(t,{A:()=>Ee});var o=n(5043),r=n(8168);const a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var i=n(2172),c=function(e,t){return o.createElement(i.A,(0,r.A)({},e,{ref:t,icon:a}))};const l=o.forwardRef(c);var s=n(8139),u=n.n(s),d=n(9635),p=n(2149),f=n(2664),m=n(8678),g=n(8574),v=n(3758),b=n(4734),h=n(5001),y=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const w={border:0,background:"transparent",padding:0,lineHeight:"inherit",display:"inline-block"},x=o.forwardRef(((e,t)=>{const{style:n,noStyle:r,disabled:a}=e,i=y(e,["style","noStyle","disabled"]);let c={};return r||(c=Object.assign({},w)),a&&(c.pointerEvents="none"),c=Object.assign(Object.assign({},c),n),o.createElement("div",Object.assign({role:"button",tabIndex:0,ref:t},i,{onKeyDown:e=>{const{keyCode:t}=e;t===h.A.ENTER&&e.preventDefault()},onKeyUp:t=>{const{keyCode:n}=t,{onClick:o}=e;n===h.A.ENTER&&o&&o()},style:c}))}));var C=n(5296),S=n(370),A=n(2481);const E={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z"}}]},name:"enter",theme:"outlined"};var O=function(e,t){return o.createElement(i.A,(0,r.A)({},e,{ref:t,icon:E}))};const k=o.forwardRef(O);var j=n(2701),I=n(798),R=n(3813),M=n(7060),N=n(6983),z=n(9310);const T=e=>{const t={};return[1,2,3,4,5].forEach((n=>{t["\n      h".concat(n,"&,\n      div&-h").concat(n,",\n      div&-h").concat(n," > textarea,\n      h").concat(n,"\n    ")]=((e,t,n,o)=>{const{titleMarginBottom:r,fontWeightStrong:a}=o;return{marginBottom:r,color:n,fontWeight:a,fontSize:e,lineHeight:t}})(e["fontSizeHeading".concat(n)],e["lineHeightHeading".concat(n)],e.colorTextHeading,e)})),t},P=e=>{const{componentCls:t}=e;return{"a&, a":Object.assign(Object.assign({},(0,R.Y)(e)),{textDecoration:e.linkDecoration,"&:active, &:hover":{textDecoration:e.linkHoverDecoration},["&[disabled], &".concat(t,"-disabled")]:{color:e.colorTextDisabled,cursor:"not-allowed","&:active, &:hover":{color:e.colorTextDisabled},"&:active":{pointerEvents:"none"}}})}},D=e=>({code:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.2em 0.1em",fontSize:"85%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3},kbd:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.15em 0.1em",fontSize:"90%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.06)",border:"1px solid rgba(100, 100, 100, 0.2)",borderBottomWidth:2,borderRadius:3},mark:{padding:0,backgroundColor:N.bK[2]},"u, ins":{textDecoration:"underline",textDecorationSkipInk:"auto"},"s, del":{textDecoration:"line-through"},strong:{fontWeight:600},"ul, ol":{marginInline:0,marginBlock:"0 1em",padding:0,li:{marginInline:"20px 0",marginBlock:0,paddingInline:"4px 0",paddingBlock:0}},ul:{listStyleType:"circle",ul:{listStyleType:"disc"}},ol:{listStyleType:"decimal"},"pre, blockquote":{margin:"1em 0"},pre:{padding:"0.4em 0.6em",whiteSpace:"pre-wrap",wordWrap:"break-word",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3,fontFamily:e.fontFamilyCode,code:{display:"inline",margin:0,padding:0,fontSize:"inherit",fontFamily:"inherit",background:"transparent",border:0}},blockquote:{paddingInline:"0.6em 0",paddingBlock:0,borderInlineStart:"4px solid rgba(100, 100, 100, 0.2)",opacity:.85}}),L=e=>{const{componentCls:t,paddingSM:n}=e,o=n;return{"&-edit-content":{position:"relative","div&":{insetInlineStart:e.calc(e.paddingSM).mul(-1).equal(),marginTop:e.calc(o).mul(-1).equal(),marginBottom:"calc(1em - ".concat((0,z.zA)(o),")")},["".concat(t,"-edit-content-confirm")]:{position:"absolute",insetInlineEnd:e.calc(e.marginXS).add(2).equal(),insetBlockEnd:e.marginXS,color:e.colorTextDescription,fontWeight:"normal",fontSize:e.fontSize,fontStyle:"normal",pointerEvents:"none"},textarea:{margin:"0!important",MozTransition:"none",height:"1em"}}}},H=e=>({["".concat(e.componentCls,"-copy-success")]:{"\n    &,\n    &:hover,\n    &:focus":{color:e.colorSuccess}},["".concat(e.componentCls,"-copy-icon-only")]:{marginInlineStart:0}}),Y=e=>{const{componentCls:t,titleMarginTop:n}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorText,wordBreak:"break-word",lineHeight:e.lineHeight,["&".concat(t,"-secondary")]:{color:e.colorTextDescription},["&".concat(t,"-success")]:{color:e.colorSuccess},["&".concat(t,"-warning")]:{color:e.colorWarning},["&".concat(t,"-danger")]:{color:e.colorError,"a&:active, a&:focus":{color:e.colorErrorActive},"a&:hover":{color:e.colorErrorHover}},["&".concat(t,"-disabled")]:{color:e.colorTextDisabled,cursor:"not-allowed",userSelect:"none"},"\n        div&,\n        p\n      ":{marginBottom:"1em"}},T(e)),{["\n      & + h1".concat(t,",\n      & + h2").concat(t,",\n      & + h3").concat(t,",\n      & + h4").concat(t,",\n      & + h5").concat(t,"\n      ")]:{marginTop:n},"\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5":{"\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        ":{marginTop:n}}}),D(e)),P(e)),{["\n        ".concat(t,"-expand,\n        ").concat(t,"-collapse,\n        ").concat(t,"-edit,\n        ").concat(t,"-copy\n      ")]:Object.assign(Object.assign({},(0,R.Y)(e)),{marginInlineStart:e.marginXXS})}),L(e)),H(e)),{"\n  a&-ellipsis,\n  span&-ellipsis\n  ":{display:"inline-block",maxWidth:"100%"},"&-single-line":{whiteSpace:"nowrap"},"&-ellipsis-single-line":{overflow:"hidden",textOverflow:"ellipsis","a&, span&":{verticalAlign:"bottom"},"> code":{paddingBlock:0,maxWidth:"calc(100% - 1.2em)",display:"inline-block",overflow:"hidden",textOverflow:"ellipsis",verticalAlign:"bottom",boxSizing:"content-box"}},"&-ellipsis-multiple-line":{display:"-webkit-box",overflow:"hidden",WebkitLineClamp:3,WebkitBoxOrient:"vertical"}}),{"&-rtl":{direction:"rtl"}})}},X=(0,M.OF)("Typography",(e=>[Y(e)]),(()=>({titleMarginTop:"1.2em",titleMarginBottom:"0.5em"}))),B=e=>{const{prefixCls:t,"aria-label":n,className:r,style:a,direction:i,maxLength:c,autoSize:l=!0,value:s,onSave:d,onCancel:p,onEnd:f,component:m,enterIcon:g=o.createElement(k,null)}=e,v=o.useRef(null),b=o.useRef(!1),y=o.useRef(),[w,x]=o.useState(s);o.useEffect((()=>{x(s)}),[s]),o.useEffect((()=>{if(v.current&&v.current.resizableTextArea){const{textArea:e}=v.current.resizableTextArea;e.focus();const{length:t}=e.value;e.setSelectionRange(t,t)}}),[]);const C=()=>{d(w.trim())},S=m?"".concat(t,"-").concat(m):"",[A,E,O]=X(t),R=u()(t,"".concat(t,"-edit-content"),{["".concat(t,"-rtl")]:"rtl"===i},r,S,E,O);return A(o.createElement("div",{className:R,style:a},o.createElement(I.A,{ref:v,maxLength:c,value:w,onChange:e=>{let{target:t}=e;x(t.value.replace(/[\n\r]/g,""))},onKeyDown:e=>{let{keyCode:t}=e;b.current||(y.current=t)},onKeyUp:e=>{let{keyCode:t,ctrlKey:n,altKey:o,metaKey:r,shiftKey:a}=e;y.current!==t||b.current||n||o||r||a||(t===h.A.ENTER?(C(),null===f||void 0===f||f()):t===h.A.ESC&&p())},onCompositionStart:()=>{b.current=!0},onCompositionEnd:()=>{b.current=!1},onBlur:()=>{C()},"aria-label":n,rows:1,autoSize:l}),null!==g?(0,j.Ob)(g,{className:"".concat(t,"-edit-content-confirm")}):null))};var W=n(5270),F=n.n(W),V=n(7483),Z=function(e,t,n,o){return new(n||(n=Promise))((function(r,a){function i(e){try{l(o.next(e))}catch(t){a(t)}}function c(e){try{l(o.throw(e))}catch(t){a(t)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,c)}l((o=o.apply(e,t||[])).next())}))};const q=e=>{let{copyConfig:t,children:n}=e;const[r,a]=o.useState(!1),[i,c]=o.useState(!1),l=o.useRef(null),s=()=>{l.current&&clearTimeout(l.current)},u={};t.format&&(u.format=t.format),o.useEffect((()=>s),[]);return{copied:r,copyLoading:i,onClick:(0,V._q)((e=>Z(void 0,void 0,void 0,(function*(){var o;null===e||void 0===e||e.preventDefault(),null===e||void 0===e||e.stopPropagation(),c(!0);try{const r="function"===typeof t.text?yield t.text():t.text;F()(r||String(n)||"",u),c(!1),a(!0),s(),l.current=setTimeout((()=>{a(!1)}),3e3),null===(o=t.onCopy)||void 0===o||o.call(t,e)}catch(r){throw c(!1),r}}))))}};function U(e,t){return o.useMemo((()=>{const n=!!e;return[n,Object.assign(Object.assign({},t),n&&"object"===typeof e?e:null)]}),[e])}const _=(e,t)=>{const n=o.useRef(!1);o.useEffect((()=>{n.current?e():n.current=!0}),t)};var K=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const G=o.forwardRef(((e,t)=>{const{prefixCls:n,component:r="article",className:a,rootClassName:i,setContentRef:c,children:l,direction:s,style:d}=e,p=K(e,["prefixCls","component","className","rootClassName","setContentRef","children","direction","style"]),{getPrefixCls:f,direction:m,typography:g}=o.useContext(C.QO),b=null!==s&&void 0!==s?s:m;let h=t;c&&(h=(0,v.K4)(t,c));const y=f("typography",n),[w,x,S]=X(y),A=u()(y,null===g||void 0===g?void 0:g.className,{["".concat(y,"-rtl")]:"rtl"===b},a,i,x,S),E=Object.assign(Object.assign({},null===g||void 0===g?void 0:g.style),d);return w(o.createElement(r,Object.assign({className:A,style:E,ref:h},p),l))}));const Q=G;var J=n(3390);const $={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"};var ee=function(e,t){return o.createElement(i.A,(0,r.A)({},e,{ref:t,icon:$}))};const te=o.forwardRef(ee);var ne=n(164);function oe(e){return!1===e?[!1,!1]:Array.isArray(e)?e:[e]}function re(e,t,n){return!0===e||void 0===e?t:e||n&&t}function ae(e){const{prefixCls:t,copied:n,locale:r={},onCopy:a,iconOnly:i,tooltips:c,icon:l,loading:s}=e,d=oe(c),p=oe(l),{copied:f,copy:m}=r,g=n?re(d[1],f):re(d[0],m),v="string"===typeof g?g:n?f:m;return o.createElement(A.A,{key:"copy",title:g},o.createElement(x,{className:u()("".concat(t,"-copy"),{["".concat(t,"-copy-success")]:n,["".concat(t,"-copy-icon-only")]:i}),onClick:a,"aria-label":v},n?re(p[1],o.createElement(J.A,null),!0):re(p[0],s?o.createElement(ne.A,null):o.createElement(te,null),!0)))}var ie=n(436);const ce=o.forwardRef(((e,t)=>{let{style:n,children:r}=e;const a=o.useRef(null);return o.useImperativeHandle(t,(()=>({isExceed:()=>{const e=a.current;return e.scrollHeight>e.clientHeight},getHeight:()=>a.current.clientHeight}))),o.createElement("span",{"aria-hidden":!0,ref:a,style:Object.assign({position:"fixed",display:"block",left:0,top:0,pointerEvents:"none",backgroundColor:"rgba(255, 0, 0, 0.65)"},n)},r)}));function le(e){const t=typeof e;return"string"===t||"number"===t}function se(e,t){let n=0;const o=[];for(let r=0;r<e.length;r+=1){if(n===t)return o;const a=e[r],i=n+(le(a)?String(a).length:1);if(i>t){const e=t-n;return o.push(String(a).slice(0,e)),o}o.push(a),n=i}return e}const ue={display:"-webkit-box",overflow:"hidden",WebkitBoxOrient:"vertical"};function de(e){const{enableMeasure:t,width:n,text:r,children:a,rows:i,expanded:c,miscDeps:l,onEllipsis:s}=e,u=o.useMemo((()=>(0,p.A)(r)),[r]),d=o.useMemo((()=>function(e){let t=0;return e.forEach((e=>{le(e)?t+=String(e).length:t+=1})),t}(u)),[r]),m=o.useMemo((()=>a(u,!1)),[r]),[g,v]=o.useState(null),b=o.useRef(null),h=o.useRef(null),y=o.useRef(null),w=o.useRef(null),[x,C]=o.useState(!1),[S,A]=o.useState(0),[E,O]=o.useState(0);(0,f.A)((()=>{A(t&&n&&d?1:0)}),[n,r,i,t,u]),(0,f.A)((()=>{var e,t,n,o;if(1===S){const r=!!(null===(e=h.current)||void 0===e?void 0:e.isExceed());A(r?2:3),v(r?[0,d]:null),C(r);const a=(null===(t=h.current)||void 0===t?void 0:t.getHeight())||0,c=(1===i?0:(null===(n=y.current)||void 0===n?void 0:n.getHeight())||0)+((null===(o=w.current)||void 0===o?void 0:o.getHeight())||0),l=Math.max(a,c);O(l+1),s(r)}}),[S]);const k=g?Math.ceil((g[0]+g[1])/2):0;(0,f.A)((()=>{var e;const[t,n]=g||[0,0];if(t!==n){const o=((null===(e=b.current)||void 0===e?void 0:e.getHeight())||0)>E;let r=k;n-t===1&&(r=o?t:n),v(o?[t,r]:[r,n])}}),[g,k]);const j=o.useMemo((()=>{if(2!==S||!g||g[0]!==g[1]){const e=a(u,!1);return 3!==S&&0!==S?o.createElement("span",{style:Object.assign(Object.assign({},ue),{WebkitLineClamp:i})},e):e}return a(c?u:se(u,g[0]),x)}),[c,S,g,u].concat((0,ie.A)(l))),I={width:n,whiteSpace:"normal",margin:0,padding:0};return o.createElement(o.Fragment,null,j,1===S&&o.createElement(o.Fragment,null,o.createElement(ce,{style:Object.assign(Object.assign(Object.assign({},I),ue),{WebkitLineClamp:i}),ref:h},m),o.createElement(ce,{style:Object.assign(Object.assign(Object.assign({},I),ue),{WebkitLineClamp:i-1}),ref:y},m),o.createElement(ce,{style:Object.assign(Object.assign(Object.assign({},I),ue),{WebkitLineClamp:1}),ref:w},a([],!0))),2===S&&g&&g[0]!==g[1]&&o.createElement(ce,{style:Object.assign(Object.assign({},I),{top:400}),ref:b},a(se(u,k),!0)))}const pe=e=>{let{enableEllipsis:t,isEllipsis:n,children:r,tooltipProps:a}=e;return(null===a||void 0===a?void 0:a.title)&&t?o.createElement(A.A,Object.assign({open:!!n&&void 0},a),r):r};var fe=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const me=o.forwardRef(((e,t)=>{var n,r,a;const{prefixCls:i,className:c,style:s,type:h,disabled:y,children:w,ellipsis:E,editable:O,copyable:k,component:j,title:I}=e,R=fe(e,["prefixCls","className","style","type","disabled","children","ellipsis","editable","copyable","component","title"]),{getPrefixCls:M,direction:N}=o.useContext(C.QO),[z]=(0,S.A)("Text"),T=o.useRef(null),P=o.useRef(null),D=M("typography",i),L=(0,g.A)(R,["mark","code","delete","underline","strong","keyboard","italic"]),[H,Y]=U(O),[X,W]=(0,m.A)(!1,{value:Y.editing}),{triggerType:F=["icon"]}=Y,V=e=>{var t;e&&(null===(t=Y.onStart)||void 0===t||t.call(Y)),W(e)};_((()=>{var e;X||null===(e=P.current)||void 0===e||e.focus()}),[X]);const Z=e=>{null===e||void 0===e||e.preventDefault(),V(!0)},K=e=>{var t;null===(t=Y.onChange)||void 0===t||t.call(Y,e),V(!1)},G=()=>{var e;null===(e=Y.onCancel)||void 0===e||e.call(Y),V(!1)},[J,$]=U(k),{copied:ee,copyLoading:te,onClick:ne}=q({copyConfig:$,children:w}),[oe,re]=o.useState(!1),[ie,ce]=o.useState(!1),[le,se]=o.useState(!1),[ue,me]=o.useState(!1),[ge,ve]=o.useState(!0),[be,he]=U(E,{expandable:!1,symbol:e=>e?null===z||void 0===z?void 0:z.collapse:null===z||void 0===z?void 0:z.expand}),[ye,we]=(0,m.A)(he.defaultExpanded||!1,{value:he.expanded}),xe=be&&(!ye||"collapsible"===he.expandable),{rows:Ce=1}=he,Se=o.useMemo((()=>xe&&(void 0!==he.suffix||he.onEllipsis||he.expandable||H||J)),[xe,he,H,J]);(0,f.A)((()=>{be&&!Se&&(re((0,b.F)("webkitLineClamp")),ce((0,b.F)("textOverflow")))}),[Se,be]);const[Ae,Ee]=o.useState(xe),Oe=o.useMemo((()=>!Se&&(1===Ce?ie:oe)),[Se,ie,oe]);(0,f.A)((()=>{Ee(Oe&&xe)}),[Oe,xe]);const ke=xe&&(Ae?ue:le),je=xe&&1===Ce&&Ae,Ie=xe&&Ce>1&&Ae,[Re,Me]=o.useState(0),Ne=e=>{var t;se(e),le!==e&&(null===(t=he.onEllipsis)||void 0===t||t.call(he,e))};o.useEffect((()=>{const e=T.current;if(be&&Ae&&e){const t=Ie?e.offsetHeight<e.scrollHeight:e.offsetWidth<e.scrollWidth;ue!==t&&me(t)}}),[be,Ae,w,Ie,ge,Re]),o.useEffect((()=>{const e=T.current;if("undefined"===typeof IntersectionObserver||!e||!Ae||!xe)return;const t=new IntersectionObserver((()=>{ve(!!e.offsetParent)}));return t.observe(e),()=>{t.disconnect()}}),[Ae,xe]);let ze={};ze=!0===he.tooltip?{title:null!==(n=Y.text)&&void 0!==n?n:w}:o.isValidElement(he.tooltip)?{title:he.tooltip}:"object"===typeof he.tooltip?Object.assign({title:null!==(r=Y.text)&&void 0!==r?r:w},he.tooltip):{title:he.tooltip};const Te=o.useMemo((()=>{const e=e=>["string","number"].includes(typeof e);if(be&&!Ae)return e(Y.text)?Y.text:e(w)?w:e(I)?I:e(ze.title)?ze.title:void 0}),[be,Ae,I,ze.title,ke]);if(X)return o.createElement(B,{value:null!==(a=Y.text)&&void 0!==a?a:"string"===typeof w?w:"",onSave:K,onCancel:G,onEnd:Y.onEnd,prefixCls:D,className:c,style:s,direction:N,component:j,maxLength:Y.maxLength,autoSize:Y.autoSize,enterIcon:Y.enterIcon});const Pe=()=>{const{expandable:e,symbol:t}=he;return e?ye&&"collapsible"!==e?null:o.createElement("a",{key:"expand",className:"".concat(D,"-").concat(ye?"collapse":"expand"),onClick:e=>((e,t)=>{var n;we(t.expanded),null===(n=he.onExpand)||void 0===n||n.call(he,e,t)})(e,{expanded:!ye}),"aria-label":ye?z.collapse:null===z||void 0===z?void 0:z.expand},"function"===typeof t?t(ye):t):null},De=()=>{if(!H)return;const{icon:e,tooltip:t}=Y,n=(0,p.A)(t)[0]||(null===z||void 0===z?void 0:z.edit),r="string"===typeof n?n:"";return F.includes("icon")?o.createElement(A.A,{key:"edit",title:!1===t?"":n},o.createElement(x,{ref:P,className:"".concat(D,"-edit"),onClick:Z,"aria-label":r},e||o.createElement(l,{role:"button"}))):null},Le=e=>[e&&Pe(),De(),J?o.createElement(ae,Object.assign({key:"copy"},$,{prefixCls:D,copied:ee,locale:z,onCopy:ne,loading:te,iconOnly:null===w||void 0===w})):null];return o.createElement(d.A,{onResize:e=>{let{offsetWidth:t}=e;Me(t)},disabled:!xe},(n=>o.createElement(pe,{tooltipProps:ze,enableEllipsis:xe,isEllipsis:ke},o.createElement(Q,Object.assign({className:u()({["".concat(D,"-").concat(h)]:h,["".concat(D,"-disabled")]:y,["".concat(D,"-ellipsis")]:be,["".concat(D,"-single-line")]:xe&&1===Ce&&!ye,["".concat(D,"-ellipsis-single-line")]:je,["".concat(D,"-ellipsis-multiple-line")]:Ie},c),prefixCls:i,style:Object.assign(Object.assign({},s),{WebkitLineClamp:Ie?Ce:void 0}),component:j,ref:(0,v.K4)(n,T,t),direction:N,onClick:F.includes("text")?Z:void 0,"aria-label":null===Te||void 0===Te?void 0:Te.toString(),title:I},L),o.createElement(de,{enableMeasure:xe&&!Ae,text:w,rows:Ce,width:Re,onEllipsis:Ne,expanded:ye,miscDeps:[ee,ye,te,H,J]},((t,n)=>{let r=t;t.length&&n&&!ye&&Te&&(r=o.createElement("span",{key:"show-content","aria-hidden":!0},r));const a=function(e,t){let{mark:n,code:r,underline:a,delete:i,strong:c,keyboard:l,italic:s}=e,u=t;function d(e,t){t&&(u=o.createElement(e,{},u))}return d("strong",c),d("u",a),d("del",i),d("code",r),d("mark",n),d("kbd",l),d("i",s),u}(e,o.createElement(o.Fragment,null,r,(e=>[e&&!ye&&o.createElement("span",{"aria-hidden":!0,key:"ellipsis"},"..."),he.suffix,Le(e)])(n)));return a}))))))}));var ge=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const ve=o.forwardRef(((e,t)=>{var{ellipsis:n,rel:r}=e,a=ge(e,["ellipsis","rel"]);const i=Object.assign(Object.assign({},a),{rel:void 0===r&&"_blank"===a.target?"noopener noreferrer":r});return delete i.navigate,o.createElement(me,Object.assign({},i,{ref:t,ellipsis:!!n,component:"a"}))})),be=o.forwardRef(((e,t)=>o.createElement(me,Object.assign({ref:t},e,{component:"div"}))));var he=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const ye=(e,t)=>{var{ellipsis:n}=e,r=he(e,["ellipsis"]);const a=o.useMemo((()=>n&&"object"===typeof n?(0,g.A)(n,["expandable","rows"]):n),[n]);return o.createElement(me,Object.assign({ref:t},r,{ellipsis:a,component:"span"}))},we=o.forwardRef(ye);var xe=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const Ce=[1,2,3,4,5],Se=o.forwardRef(((e,t)=>{const{level:n=1}=e,r=xe(e,["level"]);let a;return a=Ce.includes(n)?"h".concat(n):"h1",o.createElement(me,Object.assign({ref:t},r,{component:a}))})),Ae=Q;Ae.Text=we,Ae.Link=ve,Ae.Title=Se,Ae.Paragraph=be;const Ee=Ae},5270:(e,t,n)=>{"use strict";var o=n(139),r={"text/plain":"Text","text/html":"Url",default:"Text"};e.exports=function(e,t){var n,a,i,c,l,s,u=!1;t||(t={}),n=t.debug||!1;try{if(i=o(),c=document.createRange(),l=document.getSelection(),(s=document.createElement("span")).textContent=e,s.ariaHidden="true",s.style.all="unset",s.style.position="fixed",s.style.top=0,s.style.clip="rect(0, 0, 0, 0)",s.style.whiteSpace="pre",s.style.webkitUserSelect="text",s.style.MozUserSelect="text",s.style.msUserSelect="text",s.style.userSelect="text",s.addEventListener("copy",(function(o){if(o.stopPropagation(),t.format)if(o.preventDefault(),"undefined"===typeof o.clipboardData){n&&console.warn("unable to use e.clipboardData"),n&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var a=r[t.format]||r.default;window.clipboardData.setData(a,e)}else o.clipboardData.clearData(),o.clipboardData.setData(t.format,e);t.onCopy&&(o.preventDefault(),t.onCopy(o.clipboardData))})),document.body.appendChild(s),c.selectNodeContents(s),l.addRange(c),!document.execCommand("copy"))throw new Error("copy command was unsuccessful");u=!0}catch(d){n&&console.error("unable to copy using execCommand: ",d),n&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(t.format||"text",e),t.onCopy&&t.onCopy(window.clipboardData),u=!0}catch(d){n&&console.error("unable to copy using clipboardData: ",d),n&&console.error("falling back to prompt"),a=function(e){var t=(/mac os x/i.test(navigator.userAgent)?"\u2318":"Ctrl")+"+C";return e.replace(/#{\s*key\s*}/g,t)}("message"in t?t.message:"Copy to clipboard: #{key}, Enter"),window.prompt(a,e)}}finally{l&&("function"==typeof l.removeRange?l.removeRange(c):l.removeAllRanges()),s&&document.body.removeChild(s),i()}return u}},139:e=>{e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,n=[],o=0;o<e.rangeCount;o++)n.push(e.getRangeAt(o));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||n.forEach((function(t){e.addRange(t)})),t&&t.focus()}}}}]);
//# sourceMappingURL=196.67f66071.chunk.js.map