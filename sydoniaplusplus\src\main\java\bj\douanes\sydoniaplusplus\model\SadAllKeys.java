package bj.douanes.sydoniaplusplus.model;

import java.io.Serializable;
import java.math.BigDecimal;

import org.springframework.data.relational.core.mapping.Column;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class SadAllK<PERSON>s implements Serializable {
    
    @Column("KEY_YEAR")
    private String keyYear;
    
    @Column("KEY_CUO")
    private String keyCuo;
    
    @Column("KEY_DEC")
    private String keyDec;
    
    @Column("KEY_NBER")
    private String keyNber;
    
    @Column("SAD_NUM")
    private BigDecimal sadNum;

    @Column("ITM_NBER")
    private BigDecimal itmNber;
    
    // Getters and setters
}
