package bj.douanes.sydoniaplusplus.dto;

import java.math.BigDecimal;

import bj.douanes.sydoniaplusplus.model.SadGen;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SadInFItmDto {
    
    private String keyYear;
    private String keyCuo;
    private String keyDec;
    private String keyNber;
    private String sadRegSerial;
    private BigDecimal sadRegNber;
}
