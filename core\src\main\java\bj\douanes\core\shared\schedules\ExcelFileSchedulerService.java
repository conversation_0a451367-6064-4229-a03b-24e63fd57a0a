//package bj.douanes.core.shared.schedules;
//
//import java.io.File;
//import java.io.FileInputStream;
//import java.io.IOException;
//import java.nio.file.Files;
//import java.nio.file.Paths;
//import java.sql.Connection;
//import java.sql.DriverManager;
//import java.sql.PreparedStatement;
//import java.sql.SQLException;
//import java.sql.Types;
//import java.util.Arrays;
//
//import org.apache.poi.ss.usermodel.CellType;
//import org.apache.poi.ss.usermodel.DateUtil;
//import org.apache.poi.ss.usermodel.Row;
//import org.apache.poi.ss.usermodel.Sheet;
//import org.apache.poi.ss.usermodel.Workbook;
//import org.apache.poi.xssf.usermodel.XSSFWorkbook;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Service;
//
//@Service
//public class ExcelFileSchedulerService {
//
//    private static boolean onUse = false;
//    private static final String DIRECTORY_PATH = "/Files/xlsx";
//
//    String jdbcUrl = "*******************************************";
//    String username = "root";
//    String password = "root";
//
//    String sql = "INSERT INTO DECLARATIONS (NUMERO_BFU, MONTANT_TOTAL_FACTURE, CODE_BENEF, NOM_BENEF, CODE_TAXE, "
//            +
//            "NOM_TAXE, MONTANT_TAXE, REF_INTERNE_PMT, DATE_PMT, REF_TRANSFERT_FONDS, " +
//            "DATE_CR_TRANSFERT_FONDS, BANQUE, COMPTE_BENEF, NUMERO_DOC) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
//
//    @Value("${pwd}")
//    private String PWD;
//
//    private static void setOnUse(boolean val) {
//        onUse = val;
//    }
//
//    // @Scheduled(fixedRate = 5000)
//    public void performTask() {
//        if (onUse) {
//            System.out.println("déjà utiliser");
//            return;
//        }
//
//        System.out.println("Bienvenue");
//        setOnUse(true);
//        String fileString = PWD + DIRECTORY_PATH;
//
//        File directory = new File(fileString);
//        if (!directory.isDirectory()) {
//            directory.mkdirs();
//        }
//
//        File[] files = directory.listFiles((dir, name) -> name.endsWith(".xlsx"));
//
//        if (files != null && files.length > 0) {
//            Arrays.stream(files).forEach(this::processExcelFile);
//        } else {
//            System.out.println("Xlsx Files not exist");
//        }
//        setOnUse(false);
//    }
//
//    private void processExcelFile(File file) {
//
//        System.out.println(file.toString());
//
//        try (FileInputStream fis = new FileInputStream(file);
//                Workbook workbook = new XSSFWorkbook(fis);
//                Connection conn = DriverManager.getConnection(jdbcUrl, username, password)) {
//
//            Sheet sheet = workbook.getSheetAt(0);
//            PreparedStatement pstmt = conn.prepareStatement(sql);
//
//            for (Row row : sheet) {
//                if (row.getRowNum() == 0) { // Skip the header row
//                    continue;
//                }
//
//                System.out.println(row.getCell(0).getNumericCellValue());
//                break;
//
//                // pstmt.setString(1, row.getCell(0).getStringCellValue());
//                // pstmt.setDouble(2, row.getCell(1).getNumericCellValue());
//                // pstmt.setString(3, row.getCell(2).getStringCellValue());
//                // pstmt.setString(4, row.getCell(3).getStringCellValue());
//                // pstmt.setString(5, row.getCell(4).getStringCellValue());
//                // pstmt.setString(6, row.getCell(5).getStringCellValue());
//                // pstmt.setDouble(7, row.getCell(6).getNumericCellValue());
//                // pstmt.setString(8, row.getCell(7).getStringCellValue());
//
//                // // Handle DATE_PMT
//                // if (row.getCell(8) != null && row.getCell(8).getCellType() ==
//                // CellType.NUMERIC
//                // && DateUtil.isCellDateFormatted(row.getCell(8))) {
//                // java.util.Date datePmt = row.getCell(8).getDateCellValue();
//                // pstmt.setDate(9, new java.sql.Date(datePmt.getTime()));
//                // } else {
//                // pstmt.setNull(9, Types.DATE);
//                // }
//
//                // pstmt.setString(10, row.getCell(9).getStringCellValue());
//
//                // // Handle DATE_CR_TRANSFERT_FONDS
//                // if (row.getCell(10) != null && row.getCell(10).getCellType() ==
//                // CellType.NUMERIC
//                // && DateUtil.isCellDateFormatted(row.getCell(10))) {
//                // java.util.Date dateCrTransfert = row.getCell(10).getDateCellValue();
//                // pstmt.setDate(11, new java.sql.Date(dateCrTransfert.getTime()));
//                // } else {
//                // pstmt.setNull(11, Types.DATE);
//                // }
//
//                // pstmt.setString(12, row.getCell(11).getStringCellValue());
//                // pstmt.setString(13, row.getCell(12).getStringCellValue());
//                // pstmt.setString(14, row.getCell(13).getStringCellValue());
//
//                // pstmt.addBatch();
//            }
//
//            // pstmt.executeBatch();
//            System.out.println("Data has been inserted successfully.");
//
//            File directory = new File(PWD + DIRECTORY_PATH + "/archives/");
//            if (!directory.isDirectory()) {
//                directory.mkdirs();
//            }
//
//            // Déplace le fichier traité dans un dossier d'archive ou le supprime
//            Files.move(Paths.get(file.getAbsolutePath()),
//                    Paths.get(directory.getAbsolutePath() + '/' + file.getName()));
//
//            System.out.println("Data has been moved successfully.");
//
//        } catch (SQLException | IOException e) {
//            e.printStackTrace();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//}
