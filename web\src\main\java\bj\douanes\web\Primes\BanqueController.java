package bj.douanes.web.Primes;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.DTO.BanqueDTO;
import bj.douanes.facade.Prime.BanqueFacade;
import bj.douanes.utils.AppResponse;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("api/prime/banque")
public class BanqueController {
    
    private final BanqueFacade banqueFacade;

    @GetMapping("/all")
    public ResponseEntity<?> getAllBanques() {
        return AppResponse.ok(banqueFacade.getAllBanques());
    }

    @GetMapping("/{codeBanque}")
    public ResponseEntity<?> getBanqueById(@PathVariable String codeBanque) {
        return AppResponse.ok(banqueFacade.getBanqueById(codeBanque));
    }

    @PostMapping("/create")
    public ResponseEntity<?> createBanque(@RequestBody BanqueDTO banqueDTO) {
        return AppResponse.created(banqueFacade.createBanque(banqueDTO));
    }

    @PostMapping("/update/{codeBanque}")
    public ResponseEntity<?> updateBanque(@PathVariable String codeBanque, @RequestBody BanqueDTO banqueDTO) {
        return AppResponse.ok(banqueFacade.updateBanque(codeBanque, banqueDTO));
    }
    
    @DeleteMapping("/delete/{codeBanque}")
    public ResponseEntity<?> deleteBanque(@PathVariable String codeBanque) {
        banqueFacade.deleteBanque(codeBanque);
        return AppResponse.noContent();
    }
}
