package bj.douanes.sydoniaplusplus.repository;

import java.util.Optional;

import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.ListCrudRepository;
import org.springframework.stereotype.Repository;

import bj.douanes.sydoniaplusplus.model.SadAllKeys;
import bj.douanes.sydoniaplusplus.model.SadOccExp;

@Repository
public interface SadOccExpRepo extends ListCrudRepository<SadOccExp, SadAllKeys> {
    
    @Query("SELECT * FROM \"OPS$ASY\".SAD_OCC_EXP WHERE key_year = :#{#keys.keyYear} AND key_cuo = :#{#keys.keyCuo} AND key_dec = :#{#keys.keyDec} AND key_nber = :#{#keys.keyNber} AND sad_num = :#{#keys.sadNum}")
    Optional<SadOccExp> findByKeys(Sad<PERSON><PERSON><PERSON><PERSON>s keys);
}
