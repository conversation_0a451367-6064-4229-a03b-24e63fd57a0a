package bj.douanes.web;

import java.io.IOException;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import bj.douanes.facade.AgentFacade;
import bj.douanes.facade.UTILS.DTO;
import bj.douanes.utils.AppResponse;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("api/agents")
public class AgentController {

    public final AgentFacade agentFacade;

    @GetMapping
    public ResponseEntity<?> getAllAgents() {
        return AppResponse.ok(agentFacade.getAllAgents());
    }

    @GetMapping("{id}")
    public ResponseEntity<?> getAgentById(@PathVariable Long id) {
        return AppResponse.ok(agentFacade.getAgentById(id));
    }

    @PostMapping(consumes = "multipart/form-data")
    public ResponseEntity<?> createAgent(@RequestPart DTO.AgentDto agentDTO, 
                                        @RequestPart("imageFile") MultipartFile imageFile) {
        System.out.println("-------------DEBUT CREATING OBJECT--------------------");
        try {
            if (imageFile != null && !imageFile.isEmpty()) {
                agentDTO.setProfil(imageFile.getBytes());
            }
            return AppResponse.created(agentFacade.createAgent(agentDTO));
        } catch (IOException e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                 .body("Erreur lors de l'enregistrement de l'image.");
        }
    }


    @PutMapping("/update/{id}")
    public ResponseEntity<?> updateAgent(@RequestPart DTO.AgentDto agent, 
                                         @RequestPart("imageFile") MultipartFile imageFile,
                                         @PathVariable Long id){
        System.out.println("-------------UPDATING OBJECT--------------------");
       // return AppResponse.ok(agentFacade.updateAgent(id, agent)); 
        try {
            if (imageFile != null && !imageFile.isEmpty()) {
                agent.setProfil(imageFile.getBytes());
            }
            return AppResponse.ok(agentFacade.updateAgent(id, agent));
        } catch (IOException e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body("Erreur lors de la mise a jour de l'image.");
        }
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<?> deleteAgent(@PathVariable Long id){
        return AppResponse.ok(agentFacade.deleteAgent(id)); 
    }
}
