package bj.douanes.core.config.database;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * Configuration pour mapper les packages aux datasources
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.datasource.mapping")
public class PackageDataSourceMapping {
    
    /**
     * Mapping package -> datasource
     * Exemple:
     * packages:
     *   bj.douanes.personal: personal_db
     *   bj.douanes.transport: transport_db
     *   bj.douanes.prime: primary
     *   bj.douanes.sydoniaplusplus: oracle_db
     */
    private Map<String, String> packages = new HashMap<>();
    
    /**
     * Datasource par défaut si aucun mapping n'est trouvé
     */
    private String defaultDataSource = "primary";
    
    /**
     * Détermine la datasource à utiliser en fonction du nom de classe complet
     * @param className Le nom complet de la classe (package + nom de classe)
     * @return Le nom de la datasource à utiliser
     */
    public String getDataSourceForClass(String className) {
        // Recherche du mapping le plus spécifique (package le plus long qui correspond)
        String bestMatch = null;
        String bestDataSource = defaultDataSource;
        
        for (Map.Entry<String, String> entry : packages.entrySet()) {
            String packageName = entry.getKey();
            String dataSource = entry.getValue();
            
            if (className.startsWith(packageName)) {
                if (bestMatch == null || packageName.length() > bestMatch.length()) {
                    bestMatch = packageName;
                    bestDataSource = dataSource;
                }
            }
        }
        
        return bestDataSource;
    }
    
    /**
     * Détermine la datasource à utiliser en fonction du package
     * @param packageName Le nom du package
     * @return Le nom de la datasource à utiliser
     */
    public String getDataSourceForPackage(String packageName) {
        return packages.getOrDefault(packageName, defaultDataSource);
    }
}
