package bj.douanes.personal.service;

import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Agent;
import bj.douanes.personal.repository.AgentRepo;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;


public interface AgentServ {
    List<Agent> getAllAgents();
    Agent getAgentById(Long id);
    Agent saveAgent(Agent agent);
    void deleteAgent(Long id);  
} 

@Service
@RequiredArgsConstructor
class AgentServImpl implements AgentServ {

    private final AgentRepo agentRepository;

    public List<Agent> getAllAgents() {
        return agentRepository.findAll();
    }

    public Agent getAgentById(Long id) {
        return agentRepository.findById(id).orElse(null);
    }

    @Transactional
    public Agent saveAgent(Agent agent) {
        return agentRepository.save(agent);
    }

    public void deleteAgent(Long id) {
        agentRepository.deleteById(id);
    }
}
