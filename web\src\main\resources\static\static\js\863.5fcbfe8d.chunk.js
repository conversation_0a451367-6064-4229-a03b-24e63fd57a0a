"use strict";(self.webpackChunkfrontend_erp_douanes_benin=self.webpackChunkfrontend_erp_douanes_benin||[]).push([[863],{2863:(e,s,a)=>{a.r(s),a.d(s,{default:()=>j});var n,l=a(7528),i=a(807),c=a(4403),r=(a(9721),a(3067),a(5800)),d=a(7021),t=a(8489),o=a(9157),h=a(5444),u=a(579);const j=()=>(0,u.jsx)("div",{className:"",children:(0,u.jsxs)("div",{children:[(0,u.jsxs)("div",{className:"flex justify-content-between",children:[(0,u.jsx)(r.A.Title,{children:"Configuration"}),(0,u.jsx)(d.<PERSON><PERSON>,{type:"primary",children:"Modifier"})]}),(0,u.jsx)(t.A,{className:"flex m-auto",defaultActiveKey:"1",items:[{key:"1",label:"Tab 1",children:(0,u.jsxs)("div",{className:"flex",children:[(0,u.jsxs)("div",{className:"images",children:[(0,u.jsx)(o.A,{width:200,src:"https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"}),(0,u.jsx)(d.Ay,{children:"Change Image"})]}),(0,u.jsxs)("div",{className:"infos",children:[(0,u.jsx)(h.A,{placeholder:"Basic usage"}),(0,u.jsx)(h.A,{placeholder:"Basic usage"}),(0,u.jsx)(h.A,{placeholder:"Basic usage"}),(0,u.jsx)(h.A,{placeholder:"Basic usage"}),(0,u.jsx)(h.A,{placeholder:"Basic usage"}),(0,u.jsx)(h.A,{placeholder:"Basic usage"}),(0,u.jsx)(h.A,{placeholder:"Basic usage"})]})]})},{key:"2",label:"Tab 2",children:"Content of Tab Pane 2"},{key:"3",label:"Tab 3",children:"Content of Tab Pane 3"}]})]})});(0,i.AH)(n||(n=(0,l.A)(["\n  height: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background-size: cover;\n  /* background-image: url(","/assets/homeBackground.png); */\n"])),c.kK)}}]);
//# sourceMappingURL=863.5fcbfe8d.chunk.js.map