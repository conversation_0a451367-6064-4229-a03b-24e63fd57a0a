package bj.douanes.personal.service;

import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Agent;
import bj.douanes.personal.model.MutationNom;
import bj.douanes.personal.repository.AgentRepo;
import bj.douanes.personal.repository.MutationRepo;
import lombok.RequiredArgsConstructor;

public interface MutationServ {
    
    MutationNom getMutationById(Long id);
    MutationNom createMutation(Long id,MutationNom mutationNom);
    String deleteMutation(Long id);
    List<MutationNom> getMutationAllList();
}

@Service
@RequiredArgsConstructor
class MutationNominationImpl implements MutationServ{
    
    private final MutationRepo mutationRepo;
    private final AgentRepo agentRepo; 

    @Override
    public MutationNom getMutationById(Long id) {
        return mutationRepo.findById(id).orElse(null);
    }

    @Override
    public MutationNom createMutation(Long id, MutationNom mutationNom) {
        Agent agent = agentRepo.findById(id).orElse(null);
        mutationNom.setAgent(agent);
        return mutationRepo.save(mutationNom);
    }

    @Override
    public String deleteMutation(Long id) {
        mutationRepo.deleteById(id);
        return "Muation || Nomination supprimer";
    }

    @Override
    public List<MutationNom> getMutationAllList() {
        return mutationRepo.findAll();
    }

}