package bj.douanes.sydoniaplusplus.model;

import java.util.List;

import org.springframework.data.annotation.Transient;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "OPS$ASY", name = "SAD_INF")
public class SadInf {


    @Column("KEY_YEAR") @JsonIgnore
    private String keyYear;

    @Column("KEY_CUO") @JsonIgnore
    private String keyCuo;

    @Column("KEY_DEC") @JsonIgnore
    private String keyDec;

    @Column("KEY_NBER") @JsonIgnore
    private String keyNber;
    
    @Column("SAD_TXT")
    private String sadTxt;

    

}
