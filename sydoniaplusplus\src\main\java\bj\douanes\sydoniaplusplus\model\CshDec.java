package bj.douanes.sydoniaplusplus.model;

import java.util.List;

import org.springframework.data.annotation.Transient;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "OPS$ASY", name = "CSH_DEC")
public class CshDec {
    
    @Column("RCP_YEAR")
    private String rcpYear;

    @Column("RCP_CUO")
    private String rcpCuo;

    @Column("RCP_SERIAL")
    private String rcpSerial;

    @Column("RCP_NBER")
    private String rcpNber;

    @Column("CASH_DEC_DECLARANT")
    private String cashDec;

    @Column("CASH_USER_NBER")
    private String cashUserNber;

    @Column("CASH_TOTAL_TAXES")
    private String cashTotalTaxes;

    @Transient
    private SadGen sadGen;
}
