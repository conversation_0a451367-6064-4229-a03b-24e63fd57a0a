package bj.douanes.personal.service;
import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Agent;
import bj.douanes.personal.model.Notation;
import bj.douanes.personal.repository.AgentRepo;
import bj.douanes.personal.repository.NotationRepo;
import lombok.RequiredArgsConstructor;

public interface NotationServ {
    
    Notation getNotationById(Long id);
    Notation createNotation(Long id,Notation notation);
    String deleteNotation(Long id);
    List<Notation> getNotationAllList();
}

@Service
@RequiredArgsConstructor
class ActServImpl implements NotationServ{

    private final NotationRepo notationRepo;
    private final AgentRepo agentRepo;

    @Override
    public Notation getNotationById(Long id) {
        return notationRepo.findById(id).orElse(null);
    }

    @Override
    public Notation createNotation(Long id, Notation notation) {
        Agent agent = agentRepo.findById(id).orElse(null);
        notation.setAgent(agent);
        return notationRepo.save(notation);
    }

    @Override
    public String deleteNotation(Long id) {
        notationRepo.deleteById(id);
        return "Note supprimer";
    }

    @Override
    public List<Notation> getNotationAllList() {
        return notationRepo.findAll();
    }



}