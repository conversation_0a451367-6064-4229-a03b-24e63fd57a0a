package bj.douanes.Repo;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import bj.douanes.Model.Fiche;

@Repository
public interface FicheRepo extends JpaRepository<Fiche,Long>{

    @Query("SELECT f FROM Fiche f WHERE f.serialNumber = :serialNumber")
    Optional<Fiche> findBySerialNumber(String serialNumber);
    
}
