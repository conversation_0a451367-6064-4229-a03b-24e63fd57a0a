package bj.douanes.core.config.database;

import java.util.HashMap;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import jakarta.persistence.EntityManagerFactory;
import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
@EnableJpaRepositories(
    basePackages = "bj.douanes",
    entityManagerFactoryRef = "entityManagerFactory",
    transactionManagerRef = "transactionManager"
)
public class DataSourceConfig {

    private final DataSourceType dataSourceType;
    private final DatabaseComponent dbComponent;

    private boolean isAlreadyBuild = false;
    private Map<Object, Object> dataSourcesBuild;

    @Bean
    @Primary
    public DataSource dataSource() {
        if (!isAlreadyBuild) {
            isAlreadyBuild = !isAlreadyBuild;
            dataSourcesBuild = this.buildDataSources();
        }

        final Map<Object, Object> dataSources = dataSourcesBuild;
        final RoutingDataSource routingDataSource = new RoutingDataSource();
        routingDataSource.setTargetDataSources(dataSources);
        routingDataSource.setDefaultTargetDataSource(dataSources.get(dataSourceType.first));
        return routingDataSource;
    }

    @Bean
    @Primary
    public EntityManagerFactory entityManagerFactory() {
        if (!isAlreadyBuild) {
            isAlreadyBuild = !isAlreadyBuild;
            dataSourcesBuild = this.buildDataSources();
        }

        final Map<Object, Object> dataSources = dataSourcesBuild;
        final Map<Object, EntityManagerFactory> entityManagerFactories = buildEntityManagerFactories(dataSources);

        EntityManagerFactory defaultEmf = entityManagerFactories.get(dataSourceType.first);
        return new RoutingEntityManagerFactory(entityManagerFactories, defaultEmf);
    }

    @Bean
    @Primary
    public PlatformTransactionManager transactionManager() {
        return new JpaTransactionManager(entityManagerFactory());
    }

    private Map<Object, Object> buildDataSources() {
        final Map<Object, Object> result = new HashMap<>();

        for (String sourceType : dataSourceType.getEnvs()) {
            result.put(sourceType, this.buildDataSource(sourceType));
        }

        return result;
    }

    private Map<Object, EntityManagerFactory> buildEntityManagerFactories(Map<Object, Object> dataSources) {
        final Map<Object, EntityManagerFactory> result = new HashMap<>();

        for (Map.Entry<Object, Object> entry : dataSources.entrySet()) {
            result.put(entry.getKey(), this.buildEntityManagerFactory((DataSource) entry.getValue()));
        }

        return result;
    }

    private EntityManagerFactory buildEntityManagerFactory(DataSource dataSource) {
        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(dataSource);
        em.setPackagesToScan("bj.douanes");

        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);

        Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.hbm2ddl.auto", "none");
        properties.put("hibernate.format_sql", "true");
        properties.put("hibernate.dialect", "org.hibernate.dialect.PostgreSQLDialect");
        em.setJpaPropertyMap(properties);

        em.afterPropertiesSet();
        return em.getObject();
    }

    private DataSource buildDataSource(String sourceType) {
        final HikariConfig config = new HikariConfig();

        config.setJdbcUrl(this.dbComponent.getEnv(sourceType, "url"));
        config.setUsername(this.dbComponent.getEnv(sourceType, "username"));
        config.setPassword(this.dbComponent.getEnv(sourceType, "password"));
        config.setDriverClassName(this.dbComponent.getEnv(sourceType, "driver-class-name"));

        config.setAutoCommit(false);

        return new HikariDataSource(config);
    }
}