package bj.douanes.personal.service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Agent;
import lombok.extern.slf4j.Slf4j;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.export.JRCsvExporter;
import net.sf.jasperreports.engine.export.ooxml.JRXlsxExporter;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import net.sf.jasperreports.export.SimpleWriterExporterOutput;
import net.sf.jasperreports.export.SimpleXlsxReportConfiguration;

@Service
@Slf4j
public class JasperReportService {

    @Autowired
    private AgentServ agentService;

    private static final Logger logger = LoggerFactory.getLogger(JasperReportService.class);
    
    public byte[] generateAgentReport(Map<String, Object> parameters, String format) throws Exception {
        try {
            logger.info("Tentative de chargement du fichier myReport.jrxml");
            ClassPathResource reportResource = new ClassPathResource("reports/myReport.jrxml");
                if (!reportResource.exists()) {
                    throw new IOException("Le fichier myReport.jrxml est introuvable dans le classpath.");
                }

                InputStream reportStream = reportResource.getInputStream();
                JasperReport jasperReport = JasperCompileManager.compileReport(reportStream);
                logger.info("Compilation du fichier myReport.jrxml réussie.");

            // Récupérer les données d'agents
            List<Agent> agents = agentService.getAllAgents();
            JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(agents);

            // Remplir le rapport avec les données
            JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, dataSource);

            // Exporter le rapport selon le format
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            switch (format.toLowerCase()) {
                case "pdf":
                    JasperExportManager.exportReportToPdfStream(jasperPrint, outputStream);
                    break;
                    
                case "csv":
                    JRCsvExporter csvExporter = new JRCsvExporter();
                    csvExporter.setExporterInput(new SimpleExporterInput(jasperPrint));
                    csvExporter.setExporterOutput(new SimpleWriterExporterOutput(outputStream));
                    csvExporter.exportReport();
                    break;

                case "excel": 
                    JRXlsxExporter xlsxExporter = new JRXlsxExporter();
                    xlsxExporter.setExporterInput(new SimpleExporterInput(jasperPrint));
                    xlsxExporter.setExporterOutput(new SimpleOutputStreamExporterOutput(outputStream));

                    SimpleXlsxReportConfiguration reportConfig = new SimpleXlsxReportConfiguration();
                    reportConfig.setSheetNames(new String[]{"Agents"});
                    xlsxExporter.setConfiguration(reportConfig);

                    xlsxExporter.exportReport();
                    break;

                default:
                    throw new IllegalArgumentException("Format non pris en charge : " + format);
            } 

            return outputStream.toByteArray();

        } catch (JRException e) {
            e.printStackTrace();
            throw new RuntimeException("Erreur lors de la génération du rapport : " + e.getMessage());
        }
    }

}
