package bj.douanes.facade;

import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Service;

import bj.douanes.core.config.database.WithDatabase;
import bj.douanes.sydoniaplusplus.dto.SadInFItmDto;
import bj.douanes.sydoniaplusplus.model.CshGenCdi;
import bj.douanes.sydoniaplusplus.model.SadGen;
import bj.douanes.sydoniaplusplus.model.SadInf;
import bj.douanes.sydoniaplusplus.service.Services;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface SydoniaplusplusFacade {
    
    public List<SadGen> sadGen();

    public List<SadGen> sadGenBySerial(
            String year,
            String cuo,
            String serial,
            String nber);

    public List<SadGen> sadGenByDec(
            String year,
            String cuo,
            String dec,
            String nber);

    public List<SadGen> sadGenBySerial2(
            String year,
            String cuo,
            String serial,
            String nber);

    public List<CshGenCdi> sadCshList(
            String rcpYear,
            String rcpCuo,
            String rcpSerial,
            String rcpNber);

    public List<SadInf> sadTxtList(
            String keyYear,
            String keyCuo,
            String keyDec,
            String keyNber);

    public Set<SadInFItmDto> sadInf(String chassis);

}

@Slf4j
@Service
@RequiredArgsConstructor
class SydoniaplusplusFacadeImpl implements SydoniaplusplusFacade {

    private final Services services;

    @WithDatabase("ORACLE")
    public List<SadGen> sadGen() {
        log.info("LOAD ALL SAD_GEN");
        return services.findFirstTenDec();
    }

    @WithDatabase("ORACLE")
    public List<SadGen> sadGenBySerial(
            String year,
            String cuo,
            String serial,
            String nber) {
        log.info("LOAD DEC BY SERIAL NUMBER");
        return services.findDecBySerial(year, cuo, serial, nber);
    }

    @WithDatabase("ORACLE")
    public List<SadGen> sadGenByDec(
            String year,
            String cuo,
            String dec,
            String nber) {
        log.info("LOAD DEC BY REFERENCE NUMBER");
        return services.findDecBy(year, cuo, dec, nber);
    }

    @WithDatabase("ORACLE")
    public List<SadGen> sadGenBySerial2(
            String year,
            String cuo,
            String serial,
            String nber) {
        log.info("LOAD IM4 DEC BY SERIAL NUMBER");
        return services.findDecBySerial2(year, cuo, serial, nber);
    }

    @WithDatabase("ORACLE")
    public List<CshGenCdi> sadCshList(
            String rcpYear,
            String rcpCuo,
            String rcpSerial,
            String rcpNber) {
        log.info("LOAD QUITTANCE");
        return services.findCshGen(rcpYear, rcpCuo, rcpSerial, rcpNber);
    }

    @WithDatabase("ORACLE")
    public Set<SadInFItmDto> sadInf(String chassis) {
        log.info("LOAD DEC BY CHASSIS");
        return services.findByChassisList(chassis);
    }

    @WithDatabase("ORACLE")
    public List<SadInf> sadTxtList(
            String keyYear,
            String keyCuo,
            String keyDec,
            String keyNber) {
        log.info("LOAD SAD_TXT FOR SAD_INFO");
        return services.findBykeys(keyYear, keyCuo, keyDec, keyNber);
    }
 
}
