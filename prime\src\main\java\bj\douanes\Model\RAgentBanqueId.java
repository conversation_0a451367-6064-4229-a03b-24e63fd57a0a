package bj.douanes.Model;

import java.io.Serializable;
import java.time.LocalDate;

import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Embeddable
public class RAgentBanqueId implements Serializable{
    
    private String matricule;
    private String codeBanque;
    private LocalDate dateDebut;
}
