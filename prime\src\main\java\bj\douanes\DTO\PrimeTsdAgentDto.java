package bj.douanes.DTO;

import java.math.BigDecimal;
import java.time.LocalDate;

import lombok.Data;

@Data
public class PrimeTsdAgentDto {

    private Long idPrimeAgent;
    private BigDecimal montantBonification = BigDecimal.ZERO;
    private BigDecimal partGlobalReparti = BigDecimal.ZERO;
    private BigDecimal totalBrut = BigDecimal.ZERO;
    private BigDecimal totalArrondi = BigDecimal.ZERO;
    private BigDecimal montantIts = BigDecimal.ZERO;
    private BigDecimal itsArrondi = BigDecimal.ZERO;
    private BigDecimal montantNetPayer = BigDecimal.ZERO;
    private LocalDate createdAt;
    private LocalDate updatedAt;

    private String matricule;
    private Long idRepartition;
    
}
