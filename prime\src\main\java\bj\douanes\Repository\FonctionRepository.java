package bj.douanes.Repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import bj.douanes.Model.Fonction;

public interface FonctionRepository extends JpaRepository<Fonction, String>{

    // find fonction by code_fonction
    @Query("SELECT f FROM Fonction f WHERE f.codeFonction = :codeFonction")
    Fonction findByCodeFonction(@Param("codeFonction") String codeFonction);

    //supprimer une fonction par son code
    @Modifying
    @Query("DELETE FROM Fonction f WHERE f.codeFonction = :codeFonction")
    void deleteByCodeFonction(@Param("codeFonction") String codeFonction);
    

}
