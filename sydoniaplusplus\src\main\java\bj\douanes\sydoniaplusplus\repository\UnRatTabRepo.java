package bj.douanes.sydoniaplusplus.repository;

import java.time.LocalDate;
import java.util.Optional;

import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.ListCrudRepository;
import org.springframework.stereotype.Repository;

import bj.douanes.sydoniaplusplus.model.UnRatTab;


@Repository
public interface UnRatTabRepo extends ListCrudRepository<UnRatTab, String> {

    @Query("SELECT * FROM \"OPS$ASY\".UNRATTAB WHERE LST_OPE='U' AND CUR_COD=:sadCurCod AND EEA_DOV<=:sadRegDate AND EEA_EOV>=:sadRegDate")
    Optional<UnRatTab> findByCodAndDate(String sadCurCod, LocalDate sadRegDate);
    
}
