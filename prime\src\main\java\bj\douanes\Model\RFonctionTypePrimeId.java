package bj.douanes.Model;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Objects;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Embeddable
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RFonctionTypePrimeId implements Serializable {

    @Column(name = "code_fonction")
    private String codeFonction;

    @Column(name = "code_type_prime")
    private String codeTypePrime;

    @Column(name = "date_debut")
    private LocalDate dateDebut;

    // equals() et hashCode() indispensable
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof RFonctionTypePrimeId)) return false;
        RFonctionTypePrimeId that = (RFonctionTypePrimeId) o;
        return Objects.equals(codeFonction, that.codeFonction) &&
               Objects.equals(codeTypePrime, that.codeTypePrime) &&
               Objects.equals(dateDebut, that.dateDebut);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codeFonction, codeTypePrime, dateDebut);
    }
}

