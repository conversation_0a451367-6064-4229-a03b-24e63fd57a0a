package bj.douanes.facade;
import java.util.List;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import bj.douanes.personal.model.FinPosition;
import bj.douanes.personal.service.FinPositionServ;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface FinPositionFacade {
    
    FinPosition  getFinPositionById(Long id);
    FinPosition createFinPosition(Long agentId,Object finPosition);
    String deleteFinPosition(Long id);
    List<FinPosition> getFinPositionAllList();
    FinPosition update(Long id,Object finPosition);
}

@Slf4j
@Service
@RequiredArgsConstructor
class FinPositionImplement implements FinPositionFacade{

    private final ModelMapper modelMapper;
    private final FinPositionServ finPositionServ;

    @Override
    public FinPosition getFinPositionById(Long id) {
        return finPositionServ.getFinPositionById(id);
    }

    @Override
    public FinPosition createFinPosition(Long agentId, Object finPosition) {
       var newFinPosition = modelMapper.map(finPosition, FinPosition.class);
       return finPositionServ.createFinPosition(agentId, newFinPosition);
    }

    @Override
    public String deleteFinPosition(Long id) {
        return finPositionServ.deleteFinPosition(id);
    }

    @Override
    public List<FinPosition> getFinPositionAllList() {
        return finPositionServ.getFinPositionAllList();
    }

    @Override
    public FinPosition update(Long id, Object finPosition) {
        log.info("Implementation de la mise a jour");
        FinPosition finPosition2 = finPositionServ.getFinPositionById(id);
        modelMapper.map(finPosition, finPosition2);
        return finPositionServ.createFinPosition(id, finPosition2);
    }
    
}