package bj.douanes.sydoniaplusplus.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.ListCrudRepository;
import org.springframework.stereotype.Repository;

import bj.douanes.sydoniaplusplus.model.SadGen;
import bj.douanes.sydoniaplusplus.model.SadAllKeys;

@Repository
public interface SadGenRepo extends ListCrudRepository<SadGen, SadAllKeys> {
    
    @Query("SELECT * FROM \"OPS$ASY\".SAD_GEN WHERE ROWNUM <= 10")
    List<SadGen> findFirst10();

    @Query("SELECT * FROM \"OPS$ASY\".SAD_GEN WHERE KEY_YEAR=:year AND KEY_CUO=:cuo AND KEY_DEC=:dec AND KEY_NBER=:nber AND SAD_NUM=0 AND LST_OPE='U'")
    List<SadGen> findByValuesDec(String year, String cuo, String dec, String nber);
    
    @Query("SELECT * FROM \"OPS$ASY\".SAD_GEN WHERE KEY_YEAR=:year AND KEY_CUO=:cuo AND KEY_DEC=:dec AND KEY_NBER=:nber AND SAD_TYP_DEC='IM' AND SAD_NUM=0 AND LST_OPE='U'")
    Optional<SadGen> findByValuesDec2(String year, String cuo, String dec, String nber);

    @Query("SELECT * FROM \"OPS$ASY\".SAD_GEN WHERE KEY_YEAR=:year AND KEY_CUO=:cuo AND SAD_REG_SERIAL=:serial AND SAD_REG_NBER=:nber AND SAD_NUM=0 AND LST_OPE='U'")
    List<SadGen> findByValuesSerial(String year, String cuo, String serial, String nber);
    
    @Query("SELECT * FROM \"OPS$ASY\".SAD_GEN WHERE KEY_YEAR=:year AND KEY_CUO=:cuo AND SAD_REG_SERIAL=:serial AND SAD_REG_NBER=:nber AND SAD_TYP_DEC='IM' AND SAD_NUM=0 AND LST_OPE='U'")
    List<SadGen> findByValuesSerial2(String year, String cuo, String serial, String nber);

    @Query("SELECT * FROM \"OPS$ASY\".SAD_GEN WHERE KEY_YEAR=:year AND KEY_CUO=:cuo AND SAD_RCPT_SERIAL=:sadRcptSerial AND SAD_RCPT_NBER=:sadRcptNber AND SAD_NUM=0 AND LST_OPE='U'")
    Optional<SadGen> findByValues(String year, String cuo, String sadRcptSerial, String sadRcptNber);


}
