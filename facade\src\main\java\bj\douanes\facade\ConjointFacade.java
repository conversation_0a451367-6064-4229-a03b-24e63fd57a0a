package bj.douanes.facade;

import java.util.List;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import bj.douanes.personal.model.AgentConjoint;
import bj.douanes.personal.service.AgentConjointServ;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface ConjointFacade {
    
    AgentConjoint getConjointById(Long id);
    AgentConjoint createConjoint(Long agentId,Object conjoint);
    String deleteConjoint(Long id);
    List<AgentConjoint> getConjointAllList();
    AgentConjoint update(Long id,Object conjoint);
}

@Slf4j
@Service
@RequiredArgsConstructor
class ConjointFacadeImpl implements ConjointFacade{

    private final ModelMapper modelMapper;
    private final AgentConjointServ agentConjointServ;
    
    @Override
    public AgentConjoint getConjointById(Long id) {
       return agentConjointServ.getConjointById(id);
    }

    @Override
    public AgentConjoint createConjoint(Long agentId,Object conjoint) {
        log.info("Create conjoint");
        var newConjoint = modelMapper.map(conjoint, AgentConjoint.class);
        return agentConjointServ.createConjoint(agentId, newConjoint);
    }

    @Override
    public String deleteConjoint(Long id) {
        log.info("Deleting conjoint");
        return agentConjointServ.deleteConjoint(id);
    }

    @Override
    public List<AgentConjoint> getConjointAllList() {
        return agentConjointServ.getConjointAllList();
    }

    @Override
    public AgentConjoint update(Long id, Object conjoint) {
        log.info("Implementation de la mise a jour");
        AgentConjoint agentConjoint = agentConjointServ.getConjointById(id);
        modelMapper.map(conjoint, agentConjoint);
        return agentConjointServ.createConjoint(id, agentConjoint);
    }
    
}