package bj.douanes.core.config.database;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.PlatformTransactionManager;

import jakarta.persistence.EntityManagerFactory;

@SpringBootTest(classes = {DataSourceConfig.class, DatabaseBean.class, DatabaseComponent.class})
@TestPropertySource(properties = {
    "app.datasource.type.names=primary",
    "app.datasource.primary.url=jdbc:h2:mem:testdb",
    "app.datasource.primary.username=sa",
    "app.datasource.primary.password=",
    "app.datasource.primary.driver-class-name=org.h2.Driver"
})
public class MultiTenantJpaConfigTest {

    @Autowired
    private EntityManagerFactory entityManagerFactory;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Test
    public void testEntityManagerFactoryIsConfigured() {
        assertNotNull(entityManagerFactory);
        assertTrue(entityManagerFactory instanceof RoutingEntityManagerFactory);
    }

    @Test
    public void testTransactionManagerIsConfigured() {
        assertNotNull(transactionManager);
        assertTrue(transactionManager.getClass().getSimpleName().contains("Jpa"));
    }
}
