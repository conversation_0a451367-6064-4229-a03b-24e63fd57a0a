package bj.douanes.sydoniaplusplus.model;

import java.math.BigDecimal;

import org.springframework.data.annotation.Transient;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "OPS$ASY", name = "CSH_MEA")
public class CshMea {
    
    @Column("RCP_YEAR")
    private String rcpYear;

    @Column("RCP_CUO")
    private String rcpCuo;

    @Column("RCP_SERIAL")
    private String rcpSerial;

    @Column("RCP_NBER")
    private String rcpNber;

    @Column("CASH_MEAN_COD")
    private String cashMeanCod;

    @Column("CASH_MEAN_REFNBER")
    private String cashMeanRefNber;

    @Column("CASH_BANK_COD")
    private String cashBankCod;

    @Column("CASH_MEAN_AMOUNT")
    private BigDecimal cashMeanAmount;

    @Transient
    private UnMopTab unMopTab;

}
