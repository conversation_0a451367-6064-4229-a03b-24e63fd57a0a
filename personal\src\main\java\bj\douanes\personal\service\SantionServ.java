package bj.douanes.personal.service;

import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Agent;
import bj.douanes.personal.model.Sanctions;
import bj.douanes.personal.repository.AgentRepo;
import bj.douanes.personal.repository.SantionRepo;
import lombok.RequiredArgsConstructor;

public interface SantionServ {
    
    Sanctions getSanctionsById(Long id);
    Sanctions createSanctions(Long id,Sanctions sanction);
    String deleteSanctions(Long id);
    List<Sanctions> getSanctionsAllList();
}

    @Service
    @RequiredArgsConstructor
class SantionImpl implements SantionServ{
    
    private final AgentRepo agentRepo;
    private final SantionRepo santionRepo;

    @Override
    public Sanctions getSanctionsById(Long id) {
       return santionRepo.findById(id).orElse(null);
    }

    @Override
    public Sanctions createSanctions(Long id, Sanctions sanction) {
       Agent agent = agentRepo.findById(id).orElse(null);
       sanction.setAgent(agent);
       return santionRepo.save(sanction);
    }

    @Override
    public String deleteSanctions(Long id) {
        santionRepo.deleteById(id);
        return "Santion supprimerr";
    }

    @Override
    public List<Sanctions> getSanctionsAllList() {
        return santionRepo.findAll();
    }

}