package bj.douanes.sydoniaplusplus.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.*;

import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "OPS$ASY", name = "UNDECTAB")
public class UnDecTab {

    @Column("DEC_COD") @Id
    private String decCod;

    @Column("DEC_NAM")
    private String decNam;

    @Column("DEC_ADR")
    private String decAdr;

    @Column("DEC_AD2")
    private String decAd2;

}
