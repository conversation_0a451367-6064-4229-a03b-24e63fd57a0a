package bj.douanes.sydoniaplusplus.repository;

import java.util.List;

import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.ListCrudRepository;

import bj.douanes.sydoniaplusplus.model.CshAllKeys;
import bj.douanes.sydoniaplusplus.model.CshDec;
import bj.douanes.sydoniaplusplus.model.SadAllKeys;

public interface CshDecRepo extends ListCrudRepository<CshDec, CshAllKeys> {
 
    @Query("SELECT * FROM \"OPS$ASY\".CSH_DEC WHERE RCP_YEAR = :#{#keyss.rcpYear} AND RCP_CUO = :#{#keyss.rcpCuo} AND RCP_NBER = :#{#keyss.rcpNber}")
    List<CshDec> findCshAllByKeys(CshAllKeys keyss);
}
