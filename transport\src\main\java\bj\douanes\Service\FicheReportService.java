package bj.douanes.Service;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import bj.douanes.Model.Fiche;
import lombok.extern.slf4j.Slf4j;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

@Slf4j
@Service
public class FicheReportService {
    
    private static final Logger logger = LoggerFactory.getLogger(FicheReportService.class);

    public void exportFicheReport(Fiche fiche, OutputStream outputStream) throws Exception {
        ClassPathResource reportResource = new ClassPathResource("reports/fiche.jrxml");
        if (!reportResource.exists()) {
            throw new IOException("Fichier fiche.jrxml introuvable .");
        }

        try (InputStream reportStream = reportResource.getInputStream()) {
            // Compiler le fichier JRXML en rapport Jasper
            JasperReport jasperReport = JasperCompileManager.compileReport(reportStream);
            logger.info("Compilation du fichier fiche.jrxml réussie.");

            // Préparer les données pour le rapport
            JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(Collections.singletonList(fiche));

            // Paramètres optionnels du rapport
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("ReportTitle", "Rapport de Transport");

            // Remplir le rapport avec les données et les paramètres
            JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, dataSource);

            // Exporter le rapport en PDF
            JasperExportManager.exportReportToPdfStream(jasperPrint, outputStream);
        } catch (Exception e) {
            logger.error("Erreur lors de la génération du rapport : ", e);
            throw e;
        }
    }
}
