package bj.douanes.facade;

import java.util.List;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import bj.douanes.personal.model.Sanctions;
import bj.douanes.personal.service.SantionServ;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface SanctionFacade {
    Sanctions getSanctionById(Long id);
    Sanctions createSanctions(Long agentId,Object sanction);
    String deleteSanctions(Long id);
    List<Sanctions> getSanctionsAllList();
    Sanctions update(Long id,Object sanction);
}

@Slf4j
@Service
@RequiredArgsConstructor
class SanctionFacadeImpl implements SanctionFacade{

    private final SantionServ santionServ;
    private final ModelMapper modelMapper;

    @Override
    public Sanctions getSanctionById(Long id) {
        return santionServ.getSanctionsById(id);
    }

    @Override
    public Sanctions createSanctions(Long agentId, Object sanction) {
        var newSanction = modelMapper.map(sanction, Sanctions.class);
        return santionServ.createSanctions(agentId, newSanction);
    }

    @Override
    public String deleteSanctions(Long id) {
        return santionServ.deleteSanctions(id);
    }

    @Override
    public List<Sanctions> getSanctionsAllList() {
        return santionServ.getSanctionsAllList();
    }

    @Override
    public Sanctions update(Long id, Object sanction) {
        log.info("Implementation de la mise a jour");
        Sanctions findSanction = santionServ.getSanctionsById(id);
        modelMapper.map(sanction, findSanction);
        return santionServ.createSanctions(id, findSanction);
    }

}